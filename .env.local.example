# PathForge AI - Local Development Environment Variables
# Copy this file to .env and update with your actual values

# Version tag for images (use 'latest' for development)
VERSION=latest

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic Configuration
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# LangSmith Configuration (Optional)
LANGSMITH_API_KEY=your_langsmith_api_key_here
LANGSMITH_PROJECT=pathforge-ai-local

# OpenRouter Configuration
OPENROUTER_API_KEY=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
OPENROUTER_MODEL=qwen/qwen3-235b-a22b
OPENROUTER_BASEURL=https://openrouter.ai/api/v1

# Weather API
OPENWEATHERMAP_API_KEY=********************************

# Search API
BRAVE_SEARCH_API_KEY=BSAm3V_RwMeOMpifscQLjSfMj5y034x

# Default Model Configuration
DEFAULT_MODEL=openrouter

# Database Configuration (Supabase)
POSTGRES_HOST=aws-0-ap-southeast-1.pooler.supabase.com
POSTGRES_USER=postgres.vnpfqvauhkqpbuxdzphl
POSTGRES_PASSWORD=oa8q7Z0R46PoNcAy
POSTGRES_DB=postgres
POSTGRES_BACKEND_DB=postgres
POSTGRES_PORT=6543

# Security Secrets
JWT_SECRET=pathforge_jwt_secret_change_in_production
AUTH_SECRET=pathforge_auth_secret_change_in_production
