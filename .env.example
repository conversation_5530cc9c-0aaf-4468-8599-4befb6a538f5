# Example environment file
# Copy this to .env and fill in your actual values

# Supabase Configuration (thay thế PostgreSQL local)
SUPABASE_URL=your_supabase_project_url_here
SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_DATABASE_URL=*****************************************************/postgres

# AI Provider API Keys
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
LANGSMITH_API_KEY=your_langsmith_api_key_here
LANGSMITH_PROJECT=your_langsmith_project_here

# OpenRouter Configuration
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_MODEL=qwen/qwen3-235b-a22b
OPENROUTER_BASEURL=https://openrouter.ai/api/v1

# External API Keys
OPENWEATHERMAP_API_KEY=your_openweathermap_api_key_here
BRAVE_SEARCH_API_KEY=your_brave_search_api_key_here

# Default AI Model
DEFAULT_MODEL=openrouter

# Application Settings
NODE_ENV=development
LOG_LEVEL=INFO
DEBUG=false

# Security Keys (production)
JWT_SECRET=your_jwt_secret_change_in_production
AUTH_SECRET=your_auth_secret_change_in_production
SECRET_KEY=your-secret-key-here

# Deployment specific
VERSION=latest
