import { UserService } from '../src/services/implementations/UserService';
import { CreateUserDto } from '../src/services/interfaces';

// Mock Prisma client
const mockPrismaClient = {
  user: {
    findMany: jest.fn(),
    findUnique: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
  userSkill: {
    deleteMany: jest.fn(),
    findUnique: jest.fn(),
    create: jest.fn(),
    delete: jest.fn(),
  }
};

describe('UserService', () => {
  let userService: UserService;

  beforeEach(() => {
    jest.clearAllMocks();
    // @ts-expect-error - Using mock for testing
    userService = new UserService(mockPrismaClient);
  });

  describe('getUsers', () => {
    it('should return all users', async () => {
      const mockUsers = [
        { id: 1, name: '<PERSON>', email: '<EMAIL>' },
        { id: 2, name: '<PERSON>', email: '<EMAIL>' }
      ];

      mockPrismaClient.user.findMany.mockResolvedValue(mockUsers);

      const result = await userService.getUsers();

      expect(mockPrismaClient.user.findMany).toHaveBeenCalled();
      expect(result).toEqual(mockUsers);
    });
  });
  describe('getUserById', () => {    it('should return user when found', async () => {
      const mockUser = { id: 1, username: 'John Doe', email: '<EMAIL>', skills: [] };

      mockPrismaClient.user.findUnique.mockResolvedValue(mockUser);

      const result = await userService.getUserById(1);

      expect(mockPrismaClient.user.findUnique).toHaveBeenCalledWith({
        where: { id: 1 },
        include: {
          skills: {
            include: {
              skill: {
                include: {
                  category: true
                }
              }
            }
          }
        }
      });
      expect(result).toEqual(mockUser);
    });

    it('should return null when user not found', async () => {
      mockPrismaClient.user.findUnique.mockResolvedValue(null);

      const result = await userService.getUserById(999);

      expect(result).toBeNull();
    });
  });  describe('createUser', () => {
    it('should create a new user successfully', async () => {
      const userData: CreateUserDto = {
        name: 'Alice Johnson',
        email: '<EMAIL>'
      };

      const mockCreatedUser = {
        id: 3,
        username: 'Alice Johnson',  // Changed from name to username
        email: '<EMAIL>',
        role: 'employee'
      };

      mockPrismaClient.user.create.mockResolvedValue(mockCreatedUser);

      const result = await userService.createUser(userData);

      // Check that the service was called with correct parameters
      expect(mockPrismaClient.user.create).toHaveBeenCalledWith({
        data: {
          username: userData.name,  // Service maps name to username
          email: userData.email,
          password: expect.any(String), // Service generates a hashed password
          role: 'employee',
          avatarUrl: undefined,
          oauthId: undefined,
          oauthProvider: undefined
        }
      });
      expect(result).toEqual(mockCreatedUser);
    });

    it('should handle unique constraint violation', async () => {
      const userData: CreateUserDto = {
        name: 'Bob Wilson',
        email: '<EMAIL>'
      };

      mockPrismaClient.user.create.mockRejectedValue(new Error('Unique constraint failed'));

      await expect(userService.createUser(userData)).rejects.toThrow('Unique constraint failed');
    });
  });

  describe('updateUser', () => {
    it('should update user successfully', async () => {
      const updateData = { name: 'Updated Name' };
      const mockUpdatedUser = {
        id: 1,
        name: 'Updated Name',
        email: '<EMAIL>'
      };

      mockPrismaClient.user.update.mockResolvedValue(mockUpdatedUser);

      const result = await userService.updateUser(1, updateData);

      expect(mockPrismaClient.user.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: updateData
      });
      expect(result).toEqual(mockUpdatedUser);
    });
  });
  describe('deleteUser', () => {
    it('should delete user successfully', async () => {
      mockPrismaClient.userSkill.deleteMany.mockResolvedValue({ count: 2 });
      mockPrismaClient.user.delete.mockResolvedValue({});

      await userService.deleteUser(1);

      // Check that it first deletes the user skills
      expect(mockPrismaClient.userSkill.deleteMany).toHaveBeenCalledWith({
        where: { userId: 1 }
      });
      
      // Then deletes the user
      expect(mockPrismaClient.user.delete).toHaveBeenCalledWith({
        where: { id: 1 }
      });
    });
  });
});
