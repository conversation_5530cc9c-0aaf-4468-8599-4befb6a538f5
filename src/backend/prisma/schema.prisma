// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  //output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id           Int           @id @default(autoincrement())
  username     String        @unique
  email        String        @unique
  password     String
  role         String        @default("employee")
  avatarUrl    String?       // Profile picture URL
  oauthProvider String?      // OAuth provider (google, github, etc.)
  oauthId      String?       // OAuth provider user ID
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  skills       UserSkill[]
  chatSessions ChatSession[]
  chatMessages ChatMessage[]
}

model Skill {
  id          Int       @id @default(autoincrement())
  name        String
  description String?
  category    Category  @relation(fields: [categoryId], references: [id])
  categoryId  Int
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  users       UserSkill[]
}

model Category {
  id          Int       @id @default(autoincrement())
  name        String
  description String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  skills      Skill[]
}

model UserSkill {
  id        Int      @id @default(autoincrement())
  user      User     @relation(fields: [userId], references: [id])
  userId    Int
  skill     Skill    @relation(fields: [skillId], references: [id])
  skillId   Int
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([userId, skillId])
}

model ChatSession {
  id         Int           @id @default(autoincrement())
  sessionId  String        @unique // UUID generated by Supabase as threadId
  user       User          @relation(fields: [userId], references: [id])
  userId     Int
  title      String?       // Optional title for the chat session
  createdAt  DateTime      @default(now())
  updatedAt  DateTime      @updatedAt
  messages   ChatMessage[]
}

model ChatMessage {
  id           Int         @id @default(autoincrement())
  session      ChatSession @relation(fields: [sessionId], references: [sessionId])
  sessionId    String      // References ChatSession.sessionId (UUID)
  user         User        @relation(fields: [userId], references: [id])
  userId       Int
  messageType  String      // 'user' or 'assistant'
  content      String      // The actual message content
  messageOrder Int         // Order of message in the conversation
  metadata     Json?       // Optional metadata (timestamps, tokens, etc.)
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt

  @@index([sessionId, messageOrder])
}
