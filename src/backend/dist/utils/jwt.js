"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JwtUtils = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key-for-development';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '30d'; // Maximum expiration time - 30 days
// Ensure JWT_SECRET is treated as a string
if (!JWT_SECRET || typeof JWT_SECRET !== 'string') {
    throw new Error('JWT_SECRET must be a valid string');
}
class JwtUtils {
    static generateToken(user) {
        const payload = {
            userId: user.id,
            email: user.email,
            role: user.role
        }; // Use maximum expiration time - 30 days for long-lived sessions
        return jsonwebtoken_1.default.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
    }
    static verifyToken(token) {
        try {
            const decoded = jsonwebtoken_1.default.verify(token, JWT_SECRET);
            // Ensure the decoded token has our required fields
            if (typeof decoded === 'object' && decoded.userId && decoded.email && decoded.role) {
                return decoded;
            }
            throw new Error('Invalid token payload');
        }
        catch (error) {
            if (error instanceof jsonwebtoken_1.default.TokenExpiredError) {
                throw new Error('Token has expired');
            }
            else if (error instanceof jsonwebtoken_1.default.JsonWebTokenError) {
                throw new Error('Invalid token');
            }
            else {
                throw new Error('Token verification failed');
            }
        }
    }
    static extractTokenFromHeader(authHeader) {
        if (!authHeader) {
            throw new Error('Authorization header is missing');
        }
        if (!authHeader.startsWith('Bearer ')) {
            throw new Error('Authorization header must start with "Bearer "');
        }
        const token = authHeader.substring(7); // Remove "Bearer " prefix
        if (!token) {
            throw new Error('Token is missing from authorization header');
        }
        return token;
    }
}
exports.JwtUtils = JwtUtils;
