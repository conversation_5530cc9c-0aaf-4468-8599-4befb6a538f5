"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthController = void 0;
/**
 * @swagger
 * tags:
 *   name: Authentication
 *   description: User authentication endpoints
 */
class AuthController {
    constructor(serviceRegistry) {
        this.serviceRegistry = serviceRegistry;
        /**
         * @swagger
         * /api/auth/register:
         *   post:
         *     summary: Register a new user
         *     tags: [Authentication]
         *     requestBody:
         *       required: true
         *       content:
         *         application/json:
         *           schema:
         *             $ref: '#/components/schemas/RegisterDto'
         *     responses:
         *       201:
         *         description: User registered successfully
         *         content:
         *           application/json:
         *             schema:
         *               $ref: '#/components/schemas/AuthResponse'
         *       400:
         *         $ref: '#/components/responses/BadRequest'
         *       409:
         *         description: User already exists
         *         content:
         *           application/json:
         *             schema:
         *               $ref: '#/components/schemas/Error'
         *       500:
         *         $ref: '#/components/responses/InternalServerError'
         */ this.register = (req, res) => __awaiter(this, void 0, void 0, function* () {
            try {
                const { username, email, password } = req.body;
                if (!username || !email || !password) {
                    res.status(400).json({ error: 'Username, email, and password are required' });
                    return;
                }
                const userService = this.serviceRegistry.getUserService();
                const result = yield userService.register({ username, email, password });
                res.status(201).json(result);
            }
            catch (error) {
                const message = error instanceof Error ? error.message : 'Registration failed';
                console.error('Registration error:', error);
                if (message.includes('already exists')) {
                    res.status(409).json({ error: message });
                }
                else {
                    res.status(500).json({ error: message || 'Failed to register user' });
                }
            }
        });
        /**
         * @swagger
         * /api/auth/login:
         *   post:
         *     summary: Login user
         *     tags: [Authentication]
         *     requestBody:
         *       required: true
         *       content:
         *         application/json:
         *           schema:
         *             $ref: '#/components/schemas/LoginDto'
         *     responses:
         *       200:
         *         description: Login successful
         *         content:
         *           application/json:
         *             schema:
         *               $ref: '#/components/schemas/AuthResponse'
         *       400:
         *         $ref: '#/components/responses/BadRequest'
         *       401:
         *         description: Invalid credentials
         *         content:
         *           application/json:
         *             schema:
         *               $ref: '#/components/schemas/Error'
         *       500:
         *         $ref: '#/components/responses/InternalServerError'
         */
        this.login = (req, res) => __awaiter(this, void 0, void 0, function* () {
            try {
                const { email, password } = req.body;
                if (!email || !password) {
                    res.status(400).json({ error: 'Email and password are required' });
                    return;
                }
                const userService = this.serviceRegistry.getUserService();
                const result = yield userService.login({ email, password });
                res.status(200).json(result);
            }
            catch (error) {
                const message = error instanceof Error ? error.message : 'Login failed';
                if (message.includes('Invalid email or password')) {
                    res.status(401).json({ error: message });
                }
                else {
                    res.status(500).json({ error: 'Failed to login' });
                }
            }
        });
        /**
         * @swagger
         * /api/auth/me:
         *   get:
         *     summary: Get current user profile
         *     tags: [Authentication]
         *     security:
         *       - bearerAuth: []
         *     responses:
         *       200:
         *         description: Current user profile
         *         content:
         *           application/json:
         *             schema:
         *               $ref: '#/components/schemas/User'
         *       401:
         *         $ref: '#/components/responses/Unauthorized'
         *       500:
         *         $ref: '#/components/responses/InternalServerError'
         */
        this.getCurrentUser = (req, res) => __awaiter(this, void 0, void 0, function* () {
            try {
                if (!req.user) {
                    res.status(401).json({ error: 'User not authenticated' });
                    return;
                }
                const userService = this.serviceRegistry.getUserService();
                const user = yield userService.getUserById(req.user.userId);
                if (!user) {
                    res.status(404).json({ error: 'User not found' });
                    return;
                }
                res.status(200).json(user);
            }
            catch (error) {
                res.status(500).json({ error: 'Failed to get user profile' });
            }
        });
    }
}
exports.AuthController = AuthController;
