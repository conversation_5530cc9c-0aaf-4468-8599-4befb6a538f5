"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CategoryController = void 0;
/**
 * @swagger
 * tags:
 *   name: Categories
 *   description: Category management endpoints
 */
class CategoryController {
    constructor(serviceRegistry) {
        this.serviceRegistry = serviceRegistry;
        /**
         * @swagger
         * /api/categories:
         *   get:
         *     summary: Get all categories
         *     tags: [Categories]
         *     security:
         *       - bearerAuth: []
         *     responses:
         *       200:
         *         description: List of all categories
         *         content:
         *           application/json:
         *             schema:
         *               type: array
         *               items:
         *                 $ref: '#/components/schemas/Category'
         *       500:
         *         $ref: '#/components/responses/InternalServerError'
         */
        this.getCategories = (_, res) => __awaiter(this, void 0, void 0, function* () {
            try {
                const categoryService = this.serviceRegistry.getCategoryService();
                const categories = yield categoryService.getCategories();
                res.json(categories);
            }
            catch (error) {
                res.status(500).json({ error: 'Failed to fetch categories' });
            }
        });
        /**
         * @swagger
         * /api/categories/{id}:
         *   get:
         *     summary: Get category by ID
         *     tags: [Categories]
         *     security:
         *       - bearerAuth: []
         *     parameters:
         *       - in: path
         *         name: id
         *         required: true
         *         schema:
         *           type: integer
         *         description: Category ID
         *     responses:
         *       200:
         *         description: Category details
         *         content:
         *           application/json:
         *             schema:
         *               $ref: '#/components/schemas/Category'
         *       404:
         *         $ref: '#/components/responses/NotFound'
         *       500:
         *         $ref: '#/components/responses/InternalServerError'
         */
        this.getCategoryById = (req, res) => __awaiter(this, void 0, void 0, function* () {
            try {
                const { id } = req.params;
                const categoryService = this.serviceRegistry.getCategoryService();
                const category = yield categoryService.getCategoryById(Number(id));
                if (!category) {
                    res.status(404).json({ error: 'Category not found' });
                    return;
                }
                res.json(category);
            }
            catch (error) {
                res.status(500).json({ error: 'Failed to fetch category' });
            }
        });
        /**
         * @swagger
         * /api/categories:
         *   post:
         *     summary: Create a new category
         *     tags: [Categories]
         *     security:
         *       - bearerAuth: []
         *     requestBody:
         *       required: true
         *       content:
         *         application/json:
         *           schema:
         *             $ref: '#/components/schemas/CreateCategoryDto'
         *     responses:
         *       201:
         *         description: Category created successfully
         *         content:
         *           application/json:
         *             schema:
         *               $ref: '#/components/schemas/Category'
         *       400:
         *         $ref: '#/components/responses/BadRequest'
         *       500:
         *         $ref: '#/components/responses/InternalServerError'
         */
        this.createCategory = (req, res) => __awaiter(this, void 0, void 0, function* () {
            try {
                const { name } = req.body;
                if (!name) {
                    res.status(400).json({ error: 'Name is required' });
                    return;
                }
                const categoryService = this.serviceRegistry.getCategoryService();
                const category = yield categoryService.createCategory({ name });
                res.status(201).json(category);
            }
            catch (error) {
                res.status(500).json({ error: 'Failed to create category' });
            }
        });
        /**
         * @swagger
         * /api/categories/{id}:
         *   put:
         *     summary: Update a category
         *     tags: [Categories]
         *     security:
         *       - bearerAuth: []
         *     parameters:
         *       - in: path
         *         name: id
         *         required: true
         *         schema:
         *           type: integer
         *         description: Category ID
         *     requestBody:
         *       required: true
         *       content:
         *         application/json:
         *           schema:
         *             type: object
         *             properties:
         *               name:
         *                 type: string
         *               description:
         *                 type: string
         *     responses:
         *       200:
         *         description: Category updated successfully
         *         content:
         *           application/json:
         *             schema:
         *               $ref: '#/components/schemas/Category'
         *       404:
         *         $ref: '#/components/responses/NotFound'
         *       500:
         *         $ref: '#/components/responses/InternalServerError'
         */
        this.updateCategory = (req, res) => __awaiter(this, void 0, void 0, function* () {
            try {
                const { id } = req.params;
                const data = req.body;
                const categoryService = this.serviceRegistry.getCategoryService();
                const category = yield categoryService.updateCategory(Number(id), data);
                res.json(category);
            }
            catch (error) {
                res.status(500).json({ error: 'Failed to update category' });
            }
        });
        /**
         * @swagger
         * /api/categories/{id}:
         *   delete:
         *     summary: Delete a category
         *     tags: [Categories]
         *     security:
         *       - bearerAuth: []
         *     parameters:
         *       - in: path
         *         name: id
         *         required: true
         *         schema:
         *           type: integer
         *         description: Category ID
         *     responses:
         *       204:
         *         description: Category deleted successfully
         *       404:
         *         $ref: '#/components/responses/NotFound'
         *       500:
         *         $ref: '#/components/responses/InternalServerError'
         */
        this.deleteCategory = (req, res) => __awaiter(this, void 0, void 0, function* () {
            try {
                const { id } = req.params;
                const categoryService = this.serviceRegistry.getCategoryService();
                yield categoryService.deleteCategory(Number(id));
                res.status(204).send();
            }
            catch (error) {
                res.status(500).json({ error: 'Failed to delete category' });
            }
        });
    }
}
exports.CategoryController = CategoryController;
