"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatController = void 0;
/**
 * @swagger
 * tags:
 *   name: Chat
 *   description: Chat management endpoints
 */
class ChatController {
    constructor(serviceRegistry) {
        this.serviceRegistry = serviceRegistry;
        /**
         * @swagger
         * /api/chat/sessions:
         *   post:
         *     summary: Create a new chat session
         *     tags: [Chat]
         *     security:
         *       - bearerAuth: []
         *     requestBody:
         *       required: true
         *       content:
         *         application/json:
         *           schema:
         *             type: object
         *             properties:
         *               title:
         *                 type: string
         *                 description: Optional title for the chat session
         *     responses:
         *       201:
         *         description: Chat session created successfully
         *         content:
         *           application/json:
         *             schema:
         *               $ref: '#/components/schemas/ChatSession'
         *       401:
         *         $ref: '#/components/responses/Unauthorized'
         *       500:
         *         $ref: '#/components/responses/InternalServerError'
         */
        this.createChatSession = (req, res) => __awaiter(this, void 0, void 0, function* () {
            var _a;
            try {
                const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.userId;
                if (!userId) {
                    res.status(401).json({ error: 'User not authenticated' });
                    return;
                }
                const { title } = req.body;
                const chatService = this.serviceRegistry.getChatService();
                const result = yield chatService.createChatSession({
                    userId,
                    title
                });
                res.status(201).json(result);
            }
            catch (error) {
                console.error('Error creating chat session:', error);
                res.status(500).json({ error: 'Failed to create chat session' });
            }
        });
        /**
         * @swagger
         * /api/chat/sessions:
         *   get:
         *     summary: Get all chat sessions for the authenticated user
         *     tags: [Chat]
         *     security:
         *       - bearerAuth: []
         *     responses:
         *       200:
         *         description: List of user's chat sessions
         *         content:
         *           application/json:
         *             schema:
         *               type: array
         *               items:
         *                 $ref: '#/components/schemas/ChatSession'
         *       401:
         *         $ref: '#/components/responses/Unauthorized'
         *       500:
         *         $ref: '#/components/responses/InternalServerError'
         */
        this.getChatSessions = (req, res) => __awaiter(this, void 0, void 0, function* () {
            var _a;
            try {
                const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.userId;
                if (!userId) {
                    res.status(401).json({ error: 'User not authenticated' });
                    return;
                }
                const chatService = this.serviceRegistry.getChatService();
                const sessions = yield chatService.getChatSessionsByUser(userId);
                res.json(sessions);
            }
            catch (error) {
                console.error('Error getting chat sessions:', error);
                res.status(500).json({ error: 'Failed to get chat sessions' });
            }
        });
        /**
         * @swagger
         * /api/chat/sessions/{sessionId}:
         *   get:
         *     summary: Get a specific chat session with messages
         *     tags: [Chat]
         *     security:
         *       - bearerAuth: []
         *     parameters:
         *       - in: path
         *         name: sessionId
         *         required: true
         *         schema:
         *           type: string
         *         description: Chat session ID (threadId)
         *     responses:
         *       200:
         *         description: Chat session with messages
         *         content:
         *           application/json:
         *             schema:
         *               $ref: '#/components/schemas/ChatSessionWithMessages'
         *       404:
         *         $ref: '#/components/responses/NotFound'
         *       401:
         *         $ref: '#/components/responses/Unauthorized'
         *       500:
         *         $ref: '#/components/responses/InternalServerError'
         */
        this.getChatSession = (req, res) => __awaiter(this, void 0, void 0, function* () {
            var _a;
            try {
                const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.userId;
                if (!userId) {
                    res.status(401).json({ error: 'User not authenticated' });
                    return;
                }
                const { sessionId } = req.params;
                const chatService = this.serviceRegistry.getChatService();
                const session = yield chatService.getChatSession(sessionId);
                if (!session) {
                    res.status(404).json({ error: 'Chat session not found' });
                    return;
                }
                // Check if user owns this session
                if (session.userId !== userId) {
                    res.status(403).json({ error: 'Access denied' });
                    return;
                }
                res.json(session);
            }
            catch (error) {
                console.error('Error getting chat session:', error);
                res.status(500).json({ error: 'Failed to get chat session' });
            }
        });
        /**
         * @swagger
         * /api/chat/sessions/{sessionId}/messages:
         *   post:
         *     summary: Send a message to a chat session
         *     tags: [Chat]
         *     security:
         *       - bearerAuth: []
         *     parameters:
         *       - in: path
         *         name: sessionId
         *         required: true
         *         schema:
         *           type: string
         *         description: Chat session ID (threadId)
         *     requestBody:
         *       required: true
         *       content:
         *         application/json:
         *           schema:
         *             type: object
         *             required:
         *               - content
         *               - messageType
         *             properties:
         *               content:
         *                 type: string
         *                 description: The message content
         *               messageType:
         *                 type: string
         *                 enum: [user, assistant]
         *                 description: Type of message
         *               metadata:
         *                 type: object
         *                 description: Optional metadata
         *     responses:
         *       201:
         *         description: Message sent successfully
         *         content:
         *           application/json:
         *             schema:
         *               $ref: '#/components/schemas/ChatMessage'
         *       400:
         *         $ref: '#/components/responses/BadRequest'
         *       401:
         *         $ref: '#/components/responses/Unauthorized'
         *       404:
         *         $ref: '#/components/responses/NotFound'
         *       500:
         *         $ref: '#/components/responses/InternalServerError'
         */
        this.sendMessage = (req, res) => __awaiter(this, void 0, void 0, function* () {
            var _a;
            try {
                const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.userId;
                if (!userId) {
                    res.status(401).json({ error: 'User not authenticated' });
                    return;
                }
                const { sessionId } = req.params;
                const { content, messageType, metadata } = req.body;
                if (!content || !messageType) {
                    res.status(400).json({ error: 'Content and messageType are required' });
                    return;
                }
                if (!['user', 'assistant'].includes(messageType)) {
                    res.status(400).json({ error: 'messageType must be either "user" or "assistant"' });
                    return;
                }
                const chatService = this.serviceRegistry.getChatService();
                // Verify session exists and user owns it
                const session = yield chatService.getChatSession(sessionId);
                if (!session) {
                    res.status(404).json({ error: 'Chat session not found' });
                    return;
                }
                if (session.userId !== userId) {
                    res.status(403).json({ error: 'Access denied' });
                    return;
                }
                const result = yield chatService.createChatMessage({
                    sessionId,
                    userId,
                    messageType,
                    content,
                    metadata
                });
                res.status(201).json(result);
            }
            catch (error) {
                console.error('Error sending message:', error);
                res.status(500).json({ error: 'Failed to send message' });
            }
        });
        /**
         * @swagger
         * /api/chat/sessions/{sessionId}/messages:
         *   get:
         *     summary: Get all messages for a chat session
         *     tags: [Chat]
         *     security:
         *       - bearerAuth: []
         *     parameters:
         *       - in: path
         *         name: sessionId
         *         required: true
         *         schema:
         *           type: string
         *         description: Chat session ID (threadId)
         *     responses:
         *       200:
         *         description: List of messages in the chat session
         *         content:
         *           application/json:
         *             schema:
         *               type: array
         *               items:
         *                 $ref: '#/components/schemas/ChatMessage'
         *       401:
         *         $ref: '#/components/responses/Unauthorized'
         *       404:
         *         $ref: '#/components/responses/NotFound'
         *       500:
         *         $ref: '#/components/responses/InternalServerError'
         */
        this.getChatMessages = (req, res) => __awaiter(this, void 0, void 0, function* () {
            var _a;
            try {
                const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.userId;
                if (!userId) {
                    res.status(401).json({ error: 'User not authenticated' });
                    return;
                }
                const { sessionId } = req.params;
                const chatService = this.serviceRegistry.getChatService();
                // Verify session exists and user owns it
                const session = yield chatService.getChatSession(sessionId);
                if (!session) {
                    res.status(404).json({ error: 'Chat session not found' });
                    return;
                }
                if (session.userId !== userId) {
                    res.status(403).json({ error: 'Access denied' });
                    return;
                }
                const messages = yield chatService.getChatMessages(sessionId);
                res.json(messages);
            }
            catch (error) {
                console.error('Error getting chat messages:', error);
                res.status(500).json({ error: 'Failed to get chat messages' });
            }
        });
        /**
         * @swagger
         * /api/chat/sessions/{sessionId}:
         *   put:
         *     summary: Update chat session title
         *     tags: [Chat]
         *     security:
         *       - bearerAuth: []
         *     parameters:
         *       - in: path
         *         name: sessionId
         *         required: true
         *         schema:
         *           type: string
         *         description: Chat session ID (threadId)
         *     requestBody:
         *       required: true
         *       content:
         *         application/json:
         *           schema:
         *             type: object
         *             required:
         *               - title
         *             properties:
         *               title:
         *                 type: string
         *                 description: New title for the chat session
         *     responses:
         *       200:
         *         description: Chat session updated successfully
         *         content:
         *           application/json:
         *             schema:
         *               $ref: '#/components/schemas/ChatSession'
         *       400:
         *         $ref: '#/components/responses/BadRequest'
         *       401:
         *         $ref: '#/components/responses/Unauthorized'
         *       404:
         *         $ref: '#/components/responses/NotFound'
         *       500:
         *         $ref: '#/components/responses/InternalServerError'
         */
        this.updateChatSession = (req, res) => __awaiter(this, void 0, void 0, function* () {
            var _a;
            try {
                const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.userId;
                if (!userId) {
                    res.status(401).json({ error: 'User not authenticated' });
                    return;
                }
                const { sessionId } = req.params;
                const { title } = req.body;
                if (!title) {
                    res.status(400).json({ error: 'Title is required' });
                    return;
                }
                const chatService = this.serviceRegistry.getChatService();
                // Verify session exists and user owns it
                const session = yield chatService.getChatSession(sessionId);
                if (!session) {
                    res.status(404).json({ error: 'Chat session not found' });
                    return;
                }
                if (session.userId !== userId) {
                    res.status(403).json({ error: 'Access denied' });
                    return;
                }
                const updatedSession = yield chatService.updateChatSessionTitle(sessionId, title);
                res.json(updatedSession);
            }
            catch (error) {
                console.error('Error updating chat session:', error);
                res.status(500).json({ error: 'Failed to update chat session' });
            }
        });
        /**
         * @swagger
         * /api/chat/sessions/{sessionId}:
         *   delete:
         *     summary: Delete a chat session and all its messages
         *     tags: [Chat]
         *     security:
         *       - bearerAuth: []
         *     parameters:
         *       - in: path
         *         name: sessionId
         *         required: true
         *         schema:
         *           type: string
         *         description: Chat session ID (threadId)
         *     responses:
         *       204:
         *         description: Chat session deleted successfully
         *       401:
         *         $ref: '#/components/responses/Unauthorized'
         *       404:
         *         $ref: '#/components/responses/NotFound'
         *       500:
         *         $ref: '#/components/responses/InternalServerError'
         */
        this.deleteChatSession = (req, res) => __awaiter(this, void 0, void 0, function* () {
            var _a;
            try {
                const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.userId;
                if (!userId) {
                    res.status(401).json({ error: 'User not authenticated' });
                    return;
                }
                const { sessionId } = req.params;
                const chatService = this.serviceRegistry.getChatService();
                // Verify session exists and user owns it
                const session = yield chatService.getChatSession(sessionId);
                if (!session) {
                    res.status(404).json({ error: 'Chat session not found' });
                    return;
                }
                if (session.userId !== userId) {
                    res.status(403).json({ error: 'Access denied' });
                    return;
                }
                yield chatService.deleteChatSession(sessionId);
                res.status(204).send();
            }
            catch (error) {
                console.error('Error deleting chat session:', error);
                res.status(500).json({ error: 'Failed to delete chat session' });
            }
        });
    }
}
exports.ChatController = ChatController;
