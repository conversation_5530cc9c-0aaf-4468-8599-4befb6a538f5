"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const app_1 = __importDefault(require("./app"));
const PORT = process.env.PORT || 8080;
// Create server instance
const server = app_1.default.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
});
// Set timeouts to prevent hanging connections
server.timeout = 30000; // 30 second timeout
server.keepAliveTimeout = 30000;
server.headersTimeout = 35000;
// Handle graceful shutdown
process.on('SIGTERM', () => {
    console.log('SIGTERM signal received. Closing server gracefully...');
    server.close(() => {
        console.log('Server closed');
        process.exit(0);
    });
    // Force close if it takes too long
    setTimeout(() => {
        console.log('Forcing server close after timeout');
        process.exit(1);
    }, 10000);
});
