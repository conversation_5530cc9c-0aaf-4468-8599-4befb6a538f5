"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatService = void 0;
const uuid_1 = require("uuid");
class ChatService {
    constructor(prisma) {
        this.prisma = prisma;
        this.sessions = new Map();
        this.messages = new Map();
        this.nextSessionId = 1;
        this.nextMessageId = 1;
    }
    createChatSession(data) {
        return __awaiter(this, void 0, void 0, function* () {
            const sessionId = (0, uuid_1.v4)();
            const now = new Date();
            const session = {
                id: this.nextSessionId++,
                sessionId,
                userId: data.userId,
                title: data.title,
                createdAt: now,
                updatedAt: now,
            };
            this.sessions.set(sessionId, session);
            this.messages.set(sessionId, []);
            return session;
        });
    }
    getChatSession(sessionId) {
        return __awaiter(this, void 0, void 0, function* () {
            const session = this.sessions.get(sessionId);
            if (!session) {
                return null;
            }
            const messages = this.messages.get(sessionId) || [];
            return Object.assign(Object.assign({}, session), { messages: messages.sort((a, b) => a.messageOrder - b.messageOrder) });
        });
    }
    getChatSessionsByUser(userId) {
        return __awaiter(this, void 0, void 0, function* () {
            return Array.from(this.sessions.values())
                .filter(session => session.userId === userId)
                .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
        });
    }
    updateChatSessionTitle(sessionId, title) {
        return __awaiter(this, void 0, void 0, function* () {
            const session = this.sessions.get(sessionId);
            if (!session) {
                throw new Error('Session not found');
            }
            const updatedSession = Object.assign(Object.assign({}, session), { title, updatedAt: new Date() });
            this.sessions.set(sessionId, updatedSession);
            return updatedSession;
        });
    }
    deleteChatSession(sessionId) {
        return __awaiter(this, void 0, void 0, function* () {
            this.sessions.delete(sessionId);
            this.messages.delete(sessionId);
        });
    }
    createChatMessage(data) {
        return __awaiter(this, void 0, void 0, function* () {
            const messageOrder = yield this.getNextMessageOrder(data.sessionId);
            const now = new Date();
            const message = {
                id: this.nextMessageId++,
                sessionId: data.sessionId,
                userId: data.userId,
                messageType: data.messageType,
                content: data.content,
                messageOrder,
                metadata: data.metadata || {},
                createdAt: now,
                updatedAt: now,
            };
            const sessionMessages = this.messages.get(data.sessionId) || [];
            sessionMessages.push(message);
            this.messages.set(data.sessionId, sessionMessages);
            // Update session timestamp
            const session = this.sessions.get(data.sessionId);
            if (session) {
                session.updatedAt = now;
            }
            return message;
        });
    }
    getChatMessages(sessionId) {
        return __awaiter(this, void 0, void 0, function* () {
            const messages = this.messages.get(sessionId) || [];
            return messages.sort((a, b) => a.messageOrder - b.messageOrder);
        });
    }
    getChatMessage(messageId) {
        return __awaiter(this, void 0, void 0, function* () {
            for (const [sessionId, messages] of this.messages.entries()) {
                const message = messages.find(m => m.id === messageId);
                if (message) {
                    const session = this.sessions.get(sessionId);
                    return Object.assign(Object.assign({}, message), { session });
                }
            }
            return null;
        });
    }
    deleteChatMessage(messageId) {
        return __awaiter(this, void 0, void 0, function* () {
            for (const [sessionId, messages] of this.messages.entries()) {
                const messageIndex = messages.findIndex(m => m.id === messageId);
                if (messageIndex !== -1) {
                    messages.splice(messageIndex, 1);
                    return;
                }
            }
            throw new Error('Message not found');
        });
    }
    getNextMessageOrder(sessionId) {
        return __awaiter(this, void 0, void 0, function* () {
            const messages = this.messages.get(sessionId) || [];
            if (messages.length === 0) {
                return 1;
            }
            return Math.max(...messages.map(m => m.messageOrder)) + 1;
        });
    }
}
exports.ChatService = ChatService;
