"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserService = void 0;
const password_1 = require("../../utils/password");
const jwt_1 = require("../../utils/jwt");
class UserService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    register(data) {
        return __awaiter(this, void 0, void 0, function* () {
            // Check if user already exists
            const existingUser = yield this.getUserByEmail(data.email);
            if (existingUser) {
                throw new Error('User with this email already exists');
            } // Hash password
            const hashedPassword = yield password_1.PasswordUtils.hashPassword(data.password); // Create user
            const user = yield this.prisma.user.create({
                data: {
                    username: data.username,
                    email: data.email,
                    password: hashedPassword,
                    role: 'employee'
                }
            }); // Generate JWT token
            const token = jwt_1.JwtUtils.generateToken({
                id: user.id,
                email: user.email,
                role: user.role || 'employee'
            });
            // Return user without password
            const { password: _ } = user, userWithoutPassword = __rest(user, ["password"]);
            return {
                user: userWithoutPassword,
                token
            };
        });
    }
    login(data) {
        return __awaiter(this, void 0, void 0, function* () {
            // Find user by email
            const user = yield this.getUserByEmail(data.email);
            if (!user || !user.password) {
                throw new Error('Invalid email or password');
            }
            // Check password
            const isPasswordValid = yield password_1.PasswordUtils.comparePassword(data.password, user.password);
            if (!isPasswordValid) {
                throw new Error('Invalid email or password');
            }
            // Generate JWT token
            const token = jwt_1.JwtUtils.generateToken({
                id: user.id,
                email: user.email,
                role: user.role || 'employee'
            });
            // Return user without password
            const { password: _ } = user, userWithoutPassword = __rest(user, ["password"]);
            return {
                user: userWithoutPassword,
                token
            };
        });
    }
    getUsers() {
        return __awaiter(this, void 0, void 0, function* () {
            const users = yield this.prisma.user.findMany();
            // Remove passwords from all users
            return users.map((_a) => {
                var { password: _ } = _a, user = __rest(_a, ["password"]);
                return user;
            });
        });
    }
    getUserById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            const user = yield this.prisma.user.findUnique({
                where: { id },
                include: {
                    skills: {
                        include: {
                            skill: {
                                include: {
                                    category: true
                                }
                            }
                        }
                    }
                }
            });
            if (!user)
                return null;
            // Remove password from response
            const { password: _ } = user, userWithoutPassword = __rest(user, ["password"]);
            return userWithoutPassword;
        });
    }
    getUserByEmail(email) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.prisma.user.findUnique({
                where: { email }
            });
        });
    }
    getUserSkills(userId) {
        return __awaiter(this, void 0, void 0, function* () {
            const userSkills = yield this.prisma.userSkill.findMany({
                where: { userId },
                include: {
                    skill: {
                        include: {
                            category: true
                        }
                    }
                }
            });
            return userSkills;
        });
    }
    createUser(data) {
        return __awaiter(this, void 0, void 0, function* () {
            let hashedPassword;
            if (data.password) {
                // Validate password strength if provided
                const passwordValidation = password_1.PasswordUtils.validatePasswordStrength(data.password);
                if (!passwordValidation.isValid) {
                    throw new Error(`Password validation failed: ${passwordValidation.errors.join(', ')}`);
                }
                hashedPassword = yield password_1.PasswordUtils.hashPassword(data.password);
            }
            else {
                // Generate a temporary password if none provided (for admin creation)
                hashedPassword = yield password_1.PasswordUtils.hashPassword('TempPassword123!');
            }
            const user = yield this.prisma.user.create({
                data: {
                    username: data.name, // Use the name as username
                    email: data.email,
                    password: hashedPassword,
                    role: data.role || 'employee',
                    avatarUrl: data.avatarUrl,
                    oauthProvider: data.oauthProvider,
                    oauthId: data.oauthId
                }
            });
            // Return user without password
            const { password: _ } = user, userWithoutPassword = __rest(user, ["password"]);
            return userWithoutPassword;
        });
    }
    updateUser(id, data) {
        return __awaiter(this, void 0, void 0, function* () {
            let updateData = Object.assign({}, data);
            if (data.password) {
                // Validate password strength if updating password
                const passwordValidation = password_1.PasswordUtils.validatePasswordStrength(data.password);
                if (!passwordValidation.isValid) {
                    throw new Error(`Password validation failed: ${passwordValidation.errors.join(', ')}`);
                }
                updateData.password = yield password_1.PasswordUtils.hashPassword(data.password);
            }
            const user = yield this.prisma.user.update({
                where: { id },
                data: updateData
            });
            // Return user without password
            const { password: _ } = user, userWithoutPassword = __rest(user, ["password"]);
            return userWithoutPassword;
        });
    }
    deleteUser(id) {
        return __awaiter(this, void 0, void 0, function* () {
            // First delete all user-skill relationships
            yield this.prisma.userSkill.deleteMany({
                where: { userId: id }
            });
            // Then delete the user
            yield this.prisma.user.delete({
                where: { id }
            });
        });
    }
    addSkillToUser(userId, data) {
        return __awaiter(this, void 0, void 0, function* () {
            // Check if the relationship already exists
            const existingRelation = yield this.prisma.userSkill.findUnique({
                where: {
                    userId_skillId: {
                        userId,
                        skillId: data.skillId
                    }
                }
            });
            if (existingRelation) {
                throw new Error('User already has this skill');
            }
            return yield this.prisma.userSkill.create({
                data: {
                    userId,
                    skillId: data.skillId
                },
                include: {
                    skill: {
                        include: {
                            category: true
                        }
                    }
                }
            });
        });
    }
    removeSkillFromUser(userId, skillId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                yield this.prisma.userSkill.delete({
                    where: {
                        userId_skillId: {
                            userId,
                            skillId
                        }
                    }
                });
            }
            catch (error) {
                throw new Error('User-skill relationship not found');
            }
        });
    }
    // Convenience methods for OAuth2 compatibility
    create(data) {
        return __awaiter(this, void 0, void 0, function* () {
            const user = yield this.createUser(data);
            return yield this.getUserById(user.id);
        });
    }
    findById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.getUserById(id);
        });
    }
    findByEmail(email) {
        return __awaiter(this, void 0, void 0, function* () {
            const user = yield this.getUserByEmail(email);
            if (!user)
                return null;
            return yield this.getUserById(user.id);
        });
    }
    update(id, data) {
        return __awaiter(this, void 0, void 0, function* () {
            yield this.updateUser(id, data);
            return yield this.getUserById(id);
        });
    }
}
exports.UserService = UserService;
