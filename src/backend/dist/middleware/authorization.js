"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.requireAuthentication = exports.requireSupervisorOrAbove = exports.requirePMOOrAdmin = exports.requireAdmin = exports.authorize = void 0;
const authorize = (...allowedRoles) => {
    return (req, res, next) => {
        if (!req.user) {
            res.status(401).json({
                error: 'Unauthorized',
                message: 'Authentication required'
            });
            return;
        }
        if (allowedRoles.length === 0) {
            // If no specific roles required, just need to be authenticated
            next();
            return;
        }
        const userRole = req.user.role;
        if (!allowedRoles.includes(userRole)) {
            res.status(403).json({
                error: 'Forbidden',
                message: `Access denied. Required roles: ${allowedRoles.join(', ')}`
            });
            return;
        }
        next();
    };
};
exports.authorize = authorize;
exports.requireAdmin = (0, exports.authorize)('admin');
exports.requirePMOOrAdmin = (0, exports.authorize)('pmo', 'admin');
exports.requireSupervisorOrAbove = (0, exports.authorize)('supervisor', 'pmo', 'admin');
exports.requireAuthentication = (0, exports.authorize)();
