"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.optionalAuth = exports.authenticateToken = void 0;
const jwt_1 = require("../utils/jwt");
const authenticateToken = (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        const token = jwt_1.JwtUtils.extractTokenFromHeader(authHeader);
        const decoded = jwt_1.JwtUtils.verifyToken(token);
        req.user = decoded;
        next();
    }
    catch (error) {
        const message = error instanceof Error ? error.message : 'Authentication failed';
        res.status(401).json({
            error: 'Unauthorized',
            message
        });
    }
};
exports.authenticateToken = authenticateToken;
const optionalAuth = (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        if (authHeader) {
            const token = jwt_1.JwtUtils.extractTokenFromHeader(authHeader);
            const decoded = jwt_1.JwtUtils.verifyToken(token);
            req.user = decoded;
        }
        next();
    }
    catch (error) {
        // Continue without authentication for optional auth
        next();
    }
};
exports.optionalAuth = optionalAuth;
