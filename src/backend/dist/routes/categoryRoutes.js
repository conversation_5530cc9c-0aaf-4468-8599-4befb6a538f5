"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createCategoryRouter = createCategoryRouter;
const express_1 = require("express");
const categoryController_1 = require("../controllers/categoryController");
function createCategoryRouter(serviceRegistry) {
    const router = (0, express_1.Router)();
    const categoryController = new categoryController_1.CategoryController(serviceRegistry);
    try {
        console.log('Setting up category routes...');
        console.log('Adding route: GET /');
        router.get('/', categoryController.getCategories);
        console.log('Adding route: GET /:id');
        router.get('/:id', categoryController.getCategoryById);
        console.log('Adding route: POST /');
        router.post('/', categoryController.createCategory);
        console.log('Adding route: PUT /:id');
        router.put('/:id', categoryController.updateCategory);
        console.log('Adding route: DELETE /:id');
        router.delete('/:id', categoryController.deleteCategory);
        console.log('Category routes setup completed successfully');
    }
    catch (error) {
        console.error('Error setting up category routes:', error);
        throw error;
    }
    return router;
}
