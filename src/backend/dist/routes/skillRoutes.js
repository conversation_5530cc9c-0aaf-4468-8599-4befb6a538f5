"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createSkillRouter = createSkillRouter;
const express_1 = require("express");
const skillController_1 = require("../controllers/skillController");
function createSkillRouter(serviceRegistry) {
    const router = (0, express_1.Router)();
    const skillController = new skillController_1.SkillController(serviceRegistry);
    try {
        console.log('Setting up skill routes...');
        console.log('Adding route: GET /');
        router.get('/', skillController.getSkills);
        console.log('Adding route: GET /:id');
        router.get('/:id', skillController.getSkillById);
        console.log('Adding route: POST /');
        router.post('/', skillController.createSkill);
        console.log('Adding route: PUT /:id');
        router.put('/:id', skillController.updateSkill);
        console.log('Adding route: DELETE /:id');
        router.delete('/:id', skillController.deleteSkill);
        console.log('Skill routes setup completed successfully');
    }
    catch (error) {
        console.error('Error setting up skill routes:', error);
        throw error;
    }
    return router;
}
