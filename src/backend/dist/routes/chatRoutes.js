"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createChatRouter = createChatRouter;
const express_1 = require("express");
const chatController_1 = require("../controllers/chatController");
const auth_1 = require("../middleware/auth");
function createChatRouter(serviceRegistry) {
    const router = (0, express_1.Router)();
    const chatController = new chatController_1.ChatController(serviceRegistry);
    // All chat routes require authentication
    router.use(auth_1.authenticateToken);
    // Chat session routes
    router.post('/sessions', chatController.createChatSession);
    router.get('/sessions', chatController.getChatSessions);
    router.get('/sessions/:sessionId', chatController.getChatSession);
    router.put('/sessions/:sessionId', chatController.updateChatSession);
    router.delete('/sessions/:sessionId', chatController.deleteChatSession);
    // Chat message routes
    router.post('/sessions/:sessionId/messages', chatController.sendMessage);
    router.get('/sessions/:sessionId/messages', chatController.getChatMessages);
    return router;
}
