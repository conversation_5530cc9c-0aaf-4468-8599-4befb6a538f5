"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createSkillRouter = exports.createCategoryRouter = exports.createUserRouter = void 0;
var userRoutes_1 = require("./userRoutes");
Object.defineProperty(exports, "createUserRouter", { enumerable: true, get: function () { return userRoutes_1.createUserRouter; } });
var categoryRoutes_1 = require("./categoryRoutes");
Object.defineProperty(exports, "createCategoryRouter", { enumerable: true, get: function () { return categoryRoutes_1.createCategoryRouter; } });
var skillRoutes_1 = require("./skillRoutes");
Object.defineProperty(exports, "createSkillRouter", { enumerable: true, get: function () { return skillRoutes_1.createSkillRouter; } });
// export { createAuthRouter } from './authRoutes'; // Temporarily disabled due to module resolution issues
