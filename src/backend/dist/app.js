"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const swagger_ui_express_1 = __importDefault(require("swagger-ui-express"));
const client_1 = __importDefault(require("./prisma/client"));
const services_1 = require("./services");
const routes_1 = require("./routes");
const swagger_1 = require("./config/swagger");
const authController_1 = require("./controllers/authController");
const auth_1 = require("./middleware/auth");
const app = (0, express_1.default)();
console.log('🔧 Initializing Express app...');
// Initialize dependency injection
console.log('🔧 Creating service registry...');
const serviceRegistry = services_1.ServiceRegistry.getInstance(client_1.default);
console.log('✅ Service registry created successfully');
// Basic middleware
app.use((0, cors_1.default)({
    origin: '*', // Allow requests from any origin
    credentials: true, // Allow cookies and authorization headers
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));
app.use(express_1.default.json());
app.use(express_1.default.urlencoded({ extended: true }));
// Request logging middleware
app.use((req, res, next) => {
    console.log(`📨 ${req.method} ${req.path} - ${new Date().toISOString()}`);
    next();
});
// API Documentation
app.use('/api-docs', swagger_ui_express_1.default.serve, swagger_ui_express_1.default.setup(swagger_1.specs));
// API Info endpoint
app.get('/api', (req, res) => {
    console.log('📊 API Info endpoint called');
    try {
        res.json({
            name: 'CodePlus Platform Backend API',
            version: '1.0.0',
            description: 'REST API for managing users, skills, categories, and authentication',
            documentation: '/api-docs',
            endpoints: {
                auth: '/api/auth',
                users: '/api/users',
                categories: '/api/categories',
                skills: '/api/skills'
            },
            authEndpoints: {
                register: 'POST /api/auth/register',
                login: 'POST /api/auth/login',
                profile: 'GET /api/auth/me (requires auth)'
            }
        });
        console.log('✅ API Info response sent');
    }
    catch (error) {
        console.error('❌ Error in API Info endpoint:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});
// Routes
console.log('🔧 Setting up API routes...');
// Temporary inline auth routes for testing
const authRouter = express_1.default.Router();
const authController = new authController_1.AuthController(serviceRegistry);
authRouter.post('/register', authController.register.bind(authController));
authRouter.post('/login', authController.login.bind(authController));
authRouter.get('/me', auth_1.authenticateToken, authController.getCurrentUser.bind(authController));
app.use('/api/auth', authRouter);
app.use('/api/users', (0, routes_1.createUserRouter)(serviceRegistry));
app.use('/api/categories', (0, routes_1.createCategoryRouter)(serviceRegistry));
app.use('/api/skills', (0, routes_1.createSkillRouter)(serviceRegistry));
// 404 handler - catch all unmatched routes
app.use((req, res) => {
    res.status(404).json({ error: 'Endpoint not found' });
});
// Global error handler
app.use((error, req, res, next) => {
    console.error('Global error handler:', error);
    res.status(500).json({ error: 'Internal server error' });
});
console.log('✅ Express app initialized successfully');
exports.default = app;
