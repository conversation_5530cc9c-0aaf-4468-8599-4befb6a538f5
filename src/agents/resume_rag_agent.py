"""
Resume RAG Agent - Integration example for ResumeRetriever with LangChain agents.

This module demonstrates how to integrate the ResumeRetriever with LangChain
agents and chains to create a conversational AI that can answer questions
about people's skills, experience, and projects.
"""

import logging
from textwrap import dedent
from typing import List, Optional

from langchain_core.documents import Document
from langchain_core.messages import HumanMessage
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.runnables import RunnablePassthrough
from langchain_core.tools import tool
from langgraph.checkpoint.memory import MemorySaver
from langgraph.prebuilt import create_react_agent
from pydantic import BaseModel, Field

from agents import prompt_lib
from agents.resume_retriever import (
    ResumeRetriever,
    create_skill_retriever,
    create_work_experience_retriever,
    create_project_retriever
)
from core import get_model
from schema.models import OpenAIModelName

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# Tool input schemas for LangChain tool compliance
class ResumeSearchInput(BaseModel):
    """Input schema for general resume search tool."""
    query: str = Field(description="Search query for resume data")
    user_id: Optional[str] = Field(default=None, description="Filter by specific user ID")
    full_name: Optional[str] = Field(default=None, description="Filter by person's full name")


class SkillSearchInput(BaseModel):
    """Input schema for skill search tool."""
    query: str = Field(description="Search query for skills and technical expertise")
    user_id: Optional[str] = Field(default=None, description="Filter by specific user ID")
    full_name: Optional[str] = Field(default=None, description="Filter by person's full name")
    experience_level: Optional[str] = Field(default=None, description="Filter by experience level (e.g., '5-10 years', '10+ years')")


class WorkExperienceSearchInput(BaseModel):
    """Input schema for work experience search tool."""
    query: str = Field(description="Search query for work experience and job history")
    user_id: Optional[str] = Field(default=None, description="Filter by specific user ID")
    full_name: Optional[str] = Field(default=None, description="Filter by person's full name")
    company_name: Optional[str] = Field(default=None, description="Filter by specific company name")


class ProjectSearchInput(BaseModel):
    """Input schema for project search tool."""
    query: str = Field(description="Search query for projects and achievements")
    user_id: Optional[str] = Field(default=None, description="Filter by specific user ID")
    full_name: Optional[str] = Field(default=None, description="Filter by person's full name")
    project_name: Optional[str] = Field(default=None, description="Filter by specific project name")


class ResumeRAGAgent:
    """
    Resume RAG Agent that combines semantic retrieval with conversational AI.
    
    This agent can answer questions about people's skills, work experience,
    and projects by retrieving relevant information from the resume database
    and generating contextual responses.
    """

    def __init__(
            self,
            model_name: str = OpenAIModelName.GPT_41_MINI,
            retriever_k: int = 10,
            similarity_threshold: float = 0.0
    ):
        """
        Initialize the Resume RAG Agent.
        
        Args:
            model_name: Name of the language model to use
            retriever_k: Number of documents to retrieve
            similarity_threshold: Minimum similarity score for retrieval
        """
        self.model_name = model_name
        self.retriever_k = retriever_k
        self.similarity_threshold = similarity_threshold

        # Initialize the language model

        self.llm = get_model(model_name)

        # Initialize the general retriever
        self.general_retriever = ResumeRetriever(
            k=retriever_k,
            similarity_threshold=similarity_threshold
        )

        # Create specialized retrievers
        self.skill_retriever = create_skill_retriever(
            k=retriever_k,
            similarity_threshold=similarity_threshold
        )
        self.work_retriever = create_work_experience_retriever(
            k=retriever_k,
            similarity_threshold=similarity_threshold
        )
        self.project_retriever = create_project_retriever(
            k=retriever_k,
            similarity_threshold=similarity_threshold
        )

        # Create retriever tools for agent use
        self.retriever_tools = self._create_retriever_tools()

        # Initialize the RAG chain
        self.rag_chain = self._create_rag_chain()

        # Initialize the agent
        self.agent = self._create_agent()

    def _create_retriever_tools(self) -> List:
        """Create LangChain tools with enhanced user identification support."""

        @tool("search_resumes", args_schema=ResumeSearchInput, description="Search through all resume data including skills, work experience, and projects. Use this for general queries about people's backgrounds.")
        def search_resumes(
            query: str,
            user_id: Optional[str] = None,
            full_name: Optional[str] = None
        ) -> List[Document]:
            """
            Search through all resume data including skills, work experience, and projects.
            Use this for general queries about people's backgrounds.

            Args:
                query: Search query for resume data
                user_id: Filter by specific user ID (optional)
                full_name: Filter by person's full name (optional)

            Returns:
                List of relevant documents with resume information
            """
            # Create retriever with user filters
            retriever = ResumeRetriever(
                k=self.retriever_k,
                similarity_threshold=self.similarity_threshold,
                user_id_filter=user_id,
                metadata_filters={"full_name": full_name} if full_name and not user_id else None
            )
            return retriever.invoke(query)

        @tool("search_skills", args_schema=SkillSearchInput, description="Search specifically for skills and technical expertise. Use this when looking for people with specific technical skills or experience levels.")
        def search_skills(
            query: str,
            user_id: Optional[str] = None,
            full_name: Optional[str] = None,
            experience_level: Optional[str] = None
        ) -> List[Document]:
            """
            Search specifically for skills and technical expertise.
            Use this when looking for people with specific technical skills or experience levels.

            Args:
                query: Search query for skills and technical expertise
                user_id: Filter by specific user ID (optional)
                full_name: Filter by person's full name (optional)
                experience_level: Filter by experience level like '5-10 years', '10+ years' (optional)

            Returns:
                List of relevant documents with skill information
            """
            # Create skill retriever with enhanced filters
            retriever = create_skill_retriever(
                experience_level=experience_level,
                user_id=user_id,
                full_name=full_name,
                k=self.retriever_k,
                similarity_threshold=self.similarity_threshold
            )
            return retriever.invoke(query)

        @tool("search_work_experience", args_schema=WorkExperienceSearchInput, description="Search through work experience and job history. Use this when looking for people who worked at specific companies or in specific roles.")
        def search_work_experience(
            query: str,
            user_id: Optional[str] = None,
            full_name: Optional[str] = None,
            company_name: Optional[str] = None
        ) -> List[Document]:
            """
            Search through work experience and job history.
            Use this when looking for people who worked at specific companies or in specific roles.

            Args:
                query: Search query for work experience and job history
                user_id: Filter by specific user ID (optional)
                full_name: Filter by person's full name (optional)
                company_name: Filter by specific company name (optional)

            Returns:
                List of relevant documents with work experience information
            """
            # Create work experience retriever with enhanced filters
            retriever = create_work_experience_retriever(
                company_name=company_name,
                user_id=user_id,
                full_name=full_name,
                k=self.retriever_k,
                similarity_threshold=self.similarity_threshold
            )
            return retriever.invoke(query)

        @tool("search_projects", args_schema=ProjectSearchInput, description="Search through project portfolios and achievements. Use this when looking for people who worked on specific types of projects.")
        def search_projects(
            query: str,
            user_id: Optional[str] = None,
            full_name: Optional[str] = None,
            project_name: Optional[str] = None
        ) -> List[Document]:
            """
            Search through project portfolios and achievements.
            Use this when looking for people who worked on specific types of projects.

            Args:
                query: Search query for projects and achievements
                user_id: Filter by specific user ID (optional)
                full_name: Filter by person's full name (optional)
                project_name: Filter by specific project name (optional)

            Returns:
                List of relevant documents with project information
            """
            # Create project retriever with enhanced filters
            retriever = create_project_retriever(
                project_name=project_name,
                user_id=user_id,
                full_name=full_name,
                k=self.retriever_k,
                similarity_threshold=self.similarity_threshold
            )
            return retriever.invoke(query)

        return [search_resumes, search_skills, search_work_experience, search_projects]

    def _create_rag_chain(self):
        """Create a RAG chain for simple question-answering."""

        # Define the prompt template
        prompt = ChatPromptTemplate.from_messages([
            ("system", """You are a helpful HR assistant that can answer questions about people's skills, work experience, and projects based on resume data.
            Use the provided context to answer questions accurately and comprehensively. If the context doesn't contain enough information to answer the question, say so clearly.            
            When mentioning people, always include their full name and relevant details from their resume.            
            Context:
            {context}"""),
            ("human", "{question}")
        ])

        # Define the chain
        def format_docs(docs):
            """Format retrieved documents for the prompt."""
            if not docs:
                return "No relevant information found."

            formatted = []
            for doc in docs:
                metadata = doc.metadata
                formatted.append(
                    f"Person: {metadata.get('full_name', 'Unknown')}\n"
                    f"Type: {metadata.get('chunk_type', 'Unknown')}\n"
                    f"Content: {doc.page_content}\n"
                    f"Similarity Score: {metadata.get('similarity_score', 0):.3f}\n"
                )
            return "\n---\n".join(formatted)

        chain = (
                {"context": self.general_retriever | format_docs, "question": RunnablePassthrough()}
                | prompt
                | self.llm
                | StrOutputParser()
        )

        return chain

    def _create_agent(self):
        """Create a ReAct agent with retriever tools."""
        memory = MemorySaver()

        # System message for the agent
        system_message = dedent(prompt_lib.HR_ASSISTANT_INSTRUCTIONS)

        agent = create_react_agent(
            self.llm,
            self.retriever_tools,
            checkpointer=memory,
            state_modifier=system_message,
        )

        return agent

    def query_simple(self, question: str) -> str:
        """
        Answer a question using the simple RAG chain.
        
        Args:
            question: The question to answer
            
        Returns:
            The answer as a string
        """
        try:
            logger.info(f"Processing simple query: {question}")
            response = self.rag_chain.invoke(question)
            return response
        except Exception as e:
            logger.error(f"Error in simple query: {str(e)}")
            return f"Sorry, I encountered an error while processing your question: {str(e)}"

    def query_agent(self, question: str, thread_id: str = "default") -> str:
        """
        Answer a question using the ReAct agent.
        
        Args:
            question: The question to answer
            thread_id: Thread ID for conversation memory
            
        Returns:
            The answer as a string
        """
        try:
            logger.info(f"Processing agent query: {question}")
            config = {"configurable": {"thread_id": thread_id}}

            response = self.agent.invoke(
                {"messages": [HumanMessage(content=question)]},
                config=config
            )

            # Extract the final message content
            if response and "messages" in response:
                final_message = response["messages"][-1]
                if hasattr(final_message, 'content'):
                    return final_message.content

            return "I couldn't generate a proper response."

        except Exception as e:
            logger.error(f"Error in agent query: {str(e)}")
            return f"Sorry, I encountered an error while processing your question: {str(e)}"


# Example usage and testing functions
def demo_resume_rag_agent():
    """Demonstrate the Resume RAG Agent functionality."""
    print("🚀 Resume RAG Agent Demo")
    print("=" * 50)

    try:
        # Initialize the agent
        agent = ResumeRAGAgent()

        # Example queries
        queries = [
            "Who has Python programming skills?",
            "Find people with machine learning experience",
            "Show me software engineers who worked at tech companies",
            "What projects involve data science or AI?",
            "Who has 5-10 years of experience in backend development?"
        ]

        print("\n🤖 Testing Simple RAG Chain:")
        for i, query in enumerate(queries[:2], 1):
            print(f"\n{i}. Query: {query}")
            try:
                response = agent.query_simple(query)
                print(f"Response: {response[:200]}...")
            except Exception as e:
                print(f"Error: {str(e)}")

        print("\n🤖 Testing ReAct Agent:")
        for i, query in enumerate(queries[2:4], 1):
            print(f"\n{i}. Query: {query}")
            try:
                response = agent.query_agent(query)
                print(f"Response: {response[:200]}...")
            except Exception as e:
                print(f"Error: {str(e)}")

        print("\n✅ Demo completed successfully!")

    except Exception as e:
        print(f"❌ Demo failed: {str(e)}")


# LangGraph-compatible wrapper for integration with agents.py
def create_resume_rag_graph():
    """
    Create a LangGraph Pregel object for the Resume RAG Agent.

    This function creates a LangGraph-compatible agent that can be integrated
    into the main agent system in agents.py.

    Returns:
        Pregel: A compiled LangGraph agent
    """
    from typing import TypedDict, Annotated
    from langgraph.graph import StateGraph, add_messages, START, END
    from langgraph.checkpoint.memory import MemorySaver
    from langchain_core.messages import AIMessage

    class ResumeRAGState(TypedDict):
        """State for Resume RAG agent."""
        messages: Annotated[list, add_messages]

    # Initialize the Resume RAG Agent instance
    resume_agent = ResumeRAGAgent()

    def resume_rag_node(state: ResumeRAGState) -> ResumeRAGState:
        """
        Process user messages using the Resume RAG Agent.

        Args:
            state: Current state containing messages

        Returns:
            Updated state with agent response
        """
        messages = state["messages"]
        if not messages:
            return {"messages": [
                AIMessage(content="Hello! I can help you search through resume data. What would you like to know?")]}

        # Get the last human message
        last_message = messages[-1]
        if hasattr(last_message, 'content'):
            user_query = last_message.content
        else:
            user_query = str(last_message)

        try:
            response = resume_agent.query_agent(user_query)
            return {"messages": [AIMessage(content=response)]}
        except Exception as e:
            logger.error(f"Error in resume RAG node: {str(e)}")
            return {"messages": [AIMessage(content=f"Sorry, I encountered an error: {str(e)}")]}

    # Build the graph
    graph_builder = StateGraph(ResumeRAGState)
    graph_builder.add_node("resume_rag", resume_rag_node)
    graph_builder.add_edge(START, "resume_rag")
    graph_builder.add_edge("resume_rag", END)

    # Compile with memory
    memory = MemorySaver()
    return graph_builder.compile(checkpointer=memory)


# Create the graph instance for export
resume_rag_agent = create_resume_rag_graph()

if __name__ == "__main__":
    demo_resume_rag_agent()
