from typing import Annotated, TypedDict

from langchain_core.messages import AIMessage, HumanMessage
from langchain_core.runnables import RunnableConfig
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, StateGraph, add_messages
from langgraph.store.memory import InMemoryStore

from agents.resume_rag_agent import resume_rag_agent


class SupervisorState(TypedDict, total=False):
    """State for the learning supervisor agent."""

    messages: Annotated[list, add_messages]
    skill_profile: str
    target_profile: str
    gap_result: str
    roadmap: str


async def collect_skills(state: SupervisorState, config: RunnableConfig) -> SupervisorState:
    """Collect the user's skills using the HR agent."""
    user_id = config["configurable"].get("user_id")
    user_name = config["configurable"].get("user_name")
    query = f"List all skills for user {user_name or user_id}."
    response = await resume_rag_agent.ainvoke({"messages": [HumanMessage(content=query)]}, config=config)
    skill_text = response["messages"][-1].content
    return {"messages": [AIMessage(content=skill_text)], "skill_profile": skill_text}


async def collect_target_profile(state: SupervisorState, config: RunnableConfig) -> SupervisorState:
    """Placeholder target profile collector."""
    profile = "Target profile: Senior Python Developer"
    return {"messages": [AIMessage(content=profile)], "target_profile": profile}


async def run_gap_analysis(state: SupervisorState, config: RunnableConfig) -> SupervisorState:
    """Placeholder gap analysis between skills and target profile."""
    gap = f"Gap analysis between current skills and target profile for {config['configurable'].get('user_id')}"
    return {"messages": [AIMessage(content=gap)], "gap_result": gap}


async def suggest_roadmap(state: SupervisorState, config: RunnableConfig) -> SupervisorState:
    """Placeholder roadmap suggestion based on gap analysis."""
    roadmap = "Suggested learning roadmap based on gap analysis"
    return {"messages": [AIMessage(content=roadmap)], "roadmap": roadmap}


# Build the graph
builder = StateGraph(SupervisorState)

builder.add_node("collect_skills", collect_skills)
builder.add_node("collect_target_profile", collect_target_profile)
builder.add_node("gap_analysis", run_gap_analysis)
builder.add_node("roadmap_generation", suggest_roadmap)

builder.add_edge(START, "collect_skills")
builder.add_edge("collect_skills", "collect_target_profile")
builder.add_edge("collect_target_profile", "gap_analysis")
builder.add_edge("gap_analysis", "roadmap_generation")
builder.add_edge("roadmap_generation", END)

learning_supervisor_agent = builder.compile(checkpointer=MemorySaver(), store=InMemoryStore())
