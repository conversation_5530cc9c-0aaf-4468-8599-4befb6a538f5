# API keys for different providers
DEEPSEEK_API_KEY=
ANTHROPIC_API_KEY=
GOOGLE_API_KEY=
GROQ_API_KEY=
USE_AWS_BEDROCK=false

# OpenRouter configuration
OPENROUTER_API_KEY=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
OPENROUTER_MODEL=qwen/qwen3-235b-a22b
OPENROUTER_BASEURL=https://openrouter.ai/api/v1

# Set a default model
DEFAULT_MODEL=openrouter

#Vertex AI
GOOGLE_APPLICATION_CREDENTIALS=

# Amazon Bedrock Knowledge Base ID
AWS_KB_ID="<knowledge-base-id>"

# Use a fake model for testing
USE_FAKE_MODEL=false

# If MODEL is set to "openai-compatible", set the following
# This is just a flexible solution. If you need multiple model options, you still need to add it to models.py
COMPATIBLE_MODEL=
COMPATIBLE_API_KEY=
COMPATIBLE_BASE_URL=

# Web server configuration
HOST=0.0.0.0
PORT=8080

# Authentication secret, HTTP bearer token header is required if set
AUTH_SECRET=

# Fuckin config for LangSmith
LANGSMITH_TRACING=true
LANGSMITH_ENDPOINT="https://api.smith.langchain.com"
LANGSMITH_API_KEY="***************************************************"
LANGSMITH_PROJECT="code-pluse"

# Application mode. If the value is "dev", it will enable uvicorn reload
MODE=

# Database type.
# If the value is "postgres", then it will require Postgresql related environment variables.
# If the value is "sqlite", then you can configure optional file path via SQLITE_DB_PATH
DATABASE_TYPE=

# If DATABASE_TYPE=sqlite (Optional)
SQLITE_DB_PATH=

# If DATABASE_TYPE=postgres
# Docker Compose default values (will work with docker-compose setup)
POSTGRES_HOST=aws-0-ap-southeast-1.pooler.supabase.com
POSTGRES_USER=postgres.vnpfqvauhkqpbuxdzphl
POSTGRES_PORT=6543
POSTGRES_PASSWORD=oa8q7Z0R46PoNcAy
POSTGRES_DB=postgres

# OpenWeatherMap API key
OPENWEATHERMAP_API_KEY=********************************

# Brave Search API key
BRAVE_SEARCH_API_KEY=BSAm3V_RwMeOMpifscQLjSfMj5y034x

# Add for running ollama
# OLLAMA_MODEL=llama3.2
# Note: set OLLAMA_BASE_URL if running service in docker and ollama on bare metal
# OLLAMA_BASE_URL=http://host.docker.internal:11434

# Add for running Azure OpenAI
AZURE_OPENAI_ENDPOINT=https://codepluse-resource.cognitiveservices.azure.com/
AZURE_OPENAI_API_VERSION=2025-01-01-preview
AZURE_OPENAI_DEPLOYMENT_MAP={"gpt-4.1-mini": "gpt-4.1-mini"}
AZURE_OPENAI_API_KEY=F4ZdG0WI2J1cdT3LHYIb0c1QXddIZu1EfzT3wrf9ZKvQaXLbcWwJJQQJ99BFACqBBLyXJ3w3AAAAACOGU7hb

# Agent URL: used in Streamlit app - if not set, defaults to http://{HOST}:{PORT}
# AGENT_URL=http://0.0.0.0:8080

# PhongTn's key. Feel free to use but Don't share :)))
DEEPSEEK_API_KEY=***********************************

OPENAI_API_KEY=***********************************

# Database for Backend - Constructed from PostgreSQL variables
# DATABASE_URL will be built from POSTGRES_* variables in docker-compose