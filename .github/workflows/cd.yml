name: PathForge AI - CD Pipeline

on:
  push:
    tags: [ 'v*' ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
        - staging
        - production
      create_tag:
        description: 'Create new tag and build images'
        required: false
        default: false
        type: boolean
      version_type:
        description: 'Version increment type (only used when create_tag is true)'
        required: false
        default: 'minor'
        type: choice
        options:
        - minor
        - patch
      force_build_all:
        description: 'Force build all services regardless of changes'
        required: false
        default: false
        type: boolean

env:
  REGISTRY: registry.heroku.com
  IMAGE_NAME: pathforge-ai
  # Single Heroku app with multiple process types
  HEROKU_APP: pathforge-ai

jobs:
  auto-version:
    runs-on: ubuntu-latest
    if: github.event.inputs.create_tag == 'true'
    permissions:
      contents: write
    outputs:
      new_version: ${{ steps.bump.outputs.NEW_VERSION }}
      new_tag: ${{ steps.bump.outputs.NEW_TAG }}

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
        token: ${{ secrets.GITHUB_TOKEN }}

    - name: Configure Git
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"

    - name: Bump version and create tag
      id: bump
      env:
        VERSION_TYPE: ${{ github.event.inputs.version_type || 'minor' }}
      run: |
        chmod +x scripts/version-bump.sh
        ./scripts/version-bump.sh

    - name: Push tag
      run: |
        git push origin ${{ steps.bump.outputs.NEW_TAG }}

  get-version:
    runs-on: ubuntu-latest
    needs: [auto-version]
    if: always() && (startsWith(github.ref, 'refs/tags/v') || needs.auto-version.result == 'success')
    outputs:
      version: ${{ steps.version.outputs.VERSION }}
      tag: ${{ steps.version.outputs.TAG }}
    steps:
    - name: Set version variables
      id: version
      run: |
        if [ "${{ github.event_name }}" = "workflow_dispatch" ] && [ "${{ github.event.inputs.create_tag }}" = "true" ]; then
          VERSION="${{ needs.auto-version.outputs.new_version }}"
          TAG="${{ needs.auto-version.outputs.new_tag }}"
        else
          TAG="${{ github.ref_name }}"
          VERSION="${TAG#v}"
        fi
        echo "VERSION=$VERSION" >> $GITHUB_OUTPUT
        echo "TAG=$TAG" >> $GITHUB_OUTPUT

  build-and-push:
    needs: [auto-version, get-version]
    if: always() && (startsWith(github.ref, 'refs/tags/v') || needs.auto-version.result == 'success')
    uses: ./.github/workflows/reusable-conditional-docker-build.yml
    permissions:
      contents: read
      packages: write
      pull-requests: read
      actions: read
    with:
      push: true
      registry: registry.heroku.com
      tag-prefix: ${{ needs.get-version.outputs.version }}
      force-build-all: ${{ github.event.inputs.force_build_all == 'true' }}
    secrets:
      HEROKU_API_KEY: ${{ secrets.HEROKU_API_KEY }}
      HEROKU_EMAIL: ${{ secrets.HEROKU_EMAIL }}

  # Fallback build for frontend if main build fails
  fallback-frontend-build:
    needs: [get-version, build-and-push]
    if: failure() && needs.build-and-push.result == 'failure'
    runs-on: ubuntu-latest
    permissions:
      contents: read
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Login to Heroku Container Registry
      run: |
        echo "${{ secrets.HEROKU_API_KEY }}" | docker login --username=${{ secrets.HEROKU_EMAIL }} --password-stdin registry.heroku.com

    - name: Build and Push Frontend with Fallback Method
      run: |
        echo "🔄 Using fallback method to build and push frontend..."
        VERSION="${{ needs.get-version.outputs.version }}"
        VERSION_IMAGE="registry.heroku.com/pathforge-ai/web:$VERSION"
        LATEST_IMAGE="registry.heroku.com/pathforge-ai/web:latest"
        
        echo "📦 Building frontend with tags:"
        echo "  Version tag: $VERSION_IMAGE"
        echo "  Latest tag: $LATEST_IMAGE"
        
        # Setup dockerignore
        cp docker/.dockerignore.frontend .dockerignore
        
        # Build image with both tags
        docker build \
          --platform linux/amd64 \
          --provenance=false \
          --file docker/Dockerfile.frontend \
          --tag "$VERSION_IMAGE" \
          --tag "$LATEST_IMAGE" \
          .
        
        # Push version tag with retry
        echo "🚀 Pushing version tag: $VERSION_IMAGE"
        for i in {1..5}; do
          echo "Version push attempt $i/5..."
          if docker push "$VERSION_IMAGE"; then
            echo "✅ Successfully pushed $VERSION_IMAGE"
            break
          else
            echo "❌ Version push attempt $i failed"
            if [ $i -eq 5 ]; then
              exit 1
            fi
            sleep 10
          fi
        done
        
        # Push latest tag with retry
        echo "🚀 Pushing latest tag: $LATEST_IMAGE"
        for i in {1..5}; do
          echo "Latest push attempt $i/5..."
          if docker push "$LATEST_IMAGE"; then
            echo "✅ Successfully pushed $LATEST_IMAGE"
            break
          else
            echo "❌ Latest push attempt $i failed"
            if [ $i -eq 5 ]; then
              exit 1
            fi
            sleep 10
          fi
        done
        
        echo "🎉 Successfully pushed both version ($VERSION) and latest tags for frontend!"

  # Fallback build for streamlit if main build fails
  fallback-streamlit-build:
    needs: [get-version, build-and-push]
    if: failure() && needs.build-and-push.result == 'failure'
    runs-on: ubuntu-latest
    permissions:
      contents: read
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Free up disk space
      run: |
        echo "🧹 Freeing up disk space..."
        sudo rm -rf /usr/share/dotnet
        sudo rm -rf /opt/ghc
        sudo rm -rf /usr/local/share/boost
        sudo rm -rf "$AGENT_TOOLSDIRECTORY"
        docker system prune -af
        df -h

    - name: Set up Docker Buildx with optimized settings
      uses: docker/setup-buildx-action@v3
      with:
        driver-opts: |
          network=host
          image=moby/buildkit:v0.12.5
        buildkitd-flags: |
          --allow-insecure-entitlement network.host
          --allow-insecure-entitlement security.insecure
        buildkitd-config-inline: |
          [worker.oci]
            max-parallelism = 2
          [worker.containerd]
            max-parallelism = 2

    - name: Login to Heroku Container Registry
      run: |
        echo "${{ secrets.HEROKU_API_KEY }}" | docker login --username=${{ secrets.HEROKU_EMAIL }} --password-stdin registry.heroku.com

    - name: Build and Push Streamlit with Optimized Method
      run: |
        echo "🔄 Using optimized method to build and push streamlit..."
        VERSION="${{ needs.get-version.outputs.version }}"
        VERSION_IMAGE="registry.heroku.com/pathforge-ai/streamlit:$VERSION"
        LATEST_IMAGE="registry.heroku.com/pathforge-ai/streamlit:latest"
        
        echo "📦 Building streamlit with tags:"
        echo "  Version tag: $VERSION_IMAGE"
        echo "  Latest tag: $LATEST_IMAGE"
        
        # Setup dockerignore
        cp docker/.dockerignore .dockerignore
        
        # Try optimized build first
        echo "🔨 Attempting optimized build..."
        if timeout 2400 docker build \
          --platform linux/amd64 \
          --provenance=false \
          --no-cache \
          --memory=6g \
          --memory-swap=8g \
          --file docker/Dockerfile.streamlit \
          --tag "$VERSION_IMAGE" \
          --tag "$LATEST_IMAGE" \
          --progress=plain \
          .; then
          echo "✅ Optimized build successful"
        else
          echo "⚠️ Optimized build failed or timed out, trying lightweight build..."
          docker build \
            --platform linux/amd64 \
            --provenance=false \
            --no-cache \
            --file docker/Dockerfile.streamlit \
            --tag "$VERSION_IMAGE" \
            --tag "$LATEST_IMAGE" \
            --progress=plain \
            .
          echo "✅ Lightweight build completed"
        fi
        
        # Push version tag with retry and timeout
        echo "🚀 Pushing version tag: $VERSION_IMAGE"
        for i in {1..5}; do
          echo "Version push attempt $i/5..."
          timeout 1800 docker push "$VERSION_IMAGE" && {
            echo "✅ Successfully pushed $VERSION_IMAGE"
            break
          } || {
            echo "❌ Version push attempt $i failed or timed out"
            if [ $i -eq 5 ]; then
              echo "❌ All version push attempts failed"
              exit 1
            fi
            sleep 15
          }
        done
        
        # Push latest tag with retry and timeout
        echo "🚀 Pushing latest tag: $LATEST_IMAGE"
        for i in {1..5}; do
          echo "Latest push attempt $i/5..."
          timeout 1800 docker push "$LATEST_IMAGE" && {
            echo "✅ Successfully pushed $LATEST_IMAGE"
            break
          } || {
            echo "❌ Latest push attempt $i failed or timed out"
            if [ $i -eq 5 ]; then
              echo "❌ All latest push attempts failed"
              exit 1
            fi
            sleep 15
          }
        done
        
        echo "🎉 Successfully pushed both version ($VERSION) and latest tags for streamlit!"

  push-to-heroku-registry:
    runs-on: ubuntu-latest
    needs: [get-version, build-and-push, fallback-frontend-build, fallback-streamlit-build]
    if: always() && (needs.build-and-push.result == 'success' || needs.fallback-frontend-build.result == 'success' || needs.fallback-streamlit-build.result == 'success')
    permissions:
      contents: read

    steps:
    - name: Login to Heroku Container Registry
      run: |
        echo "${{ secrets.HEROKU_API_KEY }}" | docker login --username=${{ secrets.HEROKU_EMAIL }} --password-stdin registry.heroku.com

    - name: Push Images to Heroku Registry
      env:
        HEROKU_API_KEY: ${{ secrets.HEROKU_API_KEY }}
      run: |
        VERSION="${{ needs.get-version.outputs.version }}"
        REGISTRY="${{ env.REGISTRY }}"
        
        echo "🚀 Pushing images to Heroku Registry..."
        echo "Version: $VERSION"
        echo "Registry: $REGISTRY"
        echo ""

        # Function to push service image to Heroku registry
        push_service_image() {
          local service_name="$1"
          local process_type="$2"
          
          echo "📦 Pushing $service_name as $process_type..."
          echo "   Source Image: $REGISTRY/${{ env.HEROKU_APP }}/$service_name:$VERSION"
          echo "   Target Image: $REGISTRY/${{ env.HEROKU_APP }}/$process_type:$VERSION"
          
          # Pull the built image and retag for Heroku process type
          if docker pull "$REGISTRY/${{ env.HEROKU_APP }}/$service_name:$VERSION"; then
            docker tag "$REGISTRY/${{ env.HEROKU_APP }}/$service_name:$VERSION" "$REGISTRY/${{ env.HEROKU_APP }}/$process_type:$VERSION"
            docker tag "$REGISTRY/${{ env.HEROKU_APP }}/$service_name:$VERSION" "$REGISTRY/${{ env.HEROKU_APP }}/$process_type:latest"
            
            # Push version tag
            if docker push "$REGISTRY/${{ env.HEROKU_APP }}/$process_type:$VERSION"; then
              echo "   ✅ Successfully pushed version tag"
            else
              echo "   ❌ Failed to push version tag"
              return 1
            fi
            
            # Push latest tag
            if docker push "$REGISTRY/${{ env.HEROKU_APP }}/$process_type:latest"; then
              echo "   ✅ Successfully pushed latest tag"
            else
              echo "   ❌ Failed to push latest tag"
              return 1
            fi
          else
            echo "   ❌ Failed to pull source image"
            return 1
          fi
          echo ""
        }

        # Push frontend service if built
        if [ "${{ needs.build-and-push.outputs.frontend-built }}" = "true" ] || [ "${{ needs.fallback-frontend-build.result }}" = "success" ]; then
          push_service_image "frontend" "web"
        else
          echo "⏭️  Frontend not built, skipping push"
        fi

        # Push agent service if built  
        if [ "${{ needs.build-and-push.outputs.agent-service-built }}" = "true" ]; then
          push_service_image "agent_service" "agent"
        else
          echo "⏭️  Agent service not built, skipping push"
        fi

        # Push streamlit app if built (either main build or fallback)
        if [ "${{ needs.build-and-push.outputs.streamlit-app-built }}" = "true" ] || [ "${{ needs.fallback-streamlit-build.result }}" = "success" ]; then
          push_service_image "streamlit_app" "streamlit"
        else
          echo "⏭️  Streamlit app not built, skipping push"
        fi

        # Push backend service if built
        if [ "${{ needs.build-and-push.outputs.backend-service-built }}" = "true" ]; then
          push_service_image "backend" "api"
        else
          echo "⏭️  Backend service not built, skipping push"
        fi

        echo "🎯 Heroku registry push completed!"
        
        echo ""
        echo "## 🏷️ Pushed Image Tags Summary" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "The following images were successfully pushed to Heroku Registry with **both version and latest tags**:" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "| Service | Version Tag | Latest Tag |" >> $GITHUB_STEP_SUMMARY
        echo "|---------|-------------|------------|" >> $GITHUB_STEP_SUMMARY
        
        VERSION="${{ needs.get-version.outputs.version }}"
        
        if [ "${{ needs.build-and-push.outputs.frontend-built }}" = "true" ] || [ "${{ needs.fallback-frontend-build.result }}" = "success" ]; then
          echo "| Frontend | \`registry.heroku.com/pathforge-ai/web:$VERSION\` | \`registry.heroku.com/pathforge-ai/web:latest\` |" >> $GITHUB_STEP_SUMMARY
        fi
        
        if [ "${{ needs.build-and-push.outputs.agent-service-built }}" = "true" ]; then
          echo "| Agent Service | \`registry.heroku.com/pathforge-ai/agent:$VERSION\` | \`registry.heroku.com/pathforge-ai/agent:latest\` |" >> $GITHUB_STEP_SUMMARY
        fi
        
        if [ "${{ needs.build-and-push.outputs.streamlit-app-built }}" = "true" ] || [ "${{ needs.fallback-streamlit-build.result }}" = "success" ]; then
          echo "| Streamlit App | \`registry.heroku.com/pathforge-ai/streamlit:$VERSION\` | \`registry.heroku.com/pathforge-ai/streamlit:latest\` |" >> $GITHUB_STEP_SUMMARY
        fi
        
        if [ "${{ needs.build-and-push.outputs.backend-service-built }}" = "true" ]; then
          echo "| Backend Service | \`registry.heroku.com/pathforge-ai/api:$VERSION\` | \`registry.heroku.com/pathforge-ai/api:latest\` |" >> $GITHUB_STEP_SUMMARY
        fi
        
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "✅ **Both version ($VERSION) and latest tags** are now available in Heroku Registry for all services!" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "💡 **Note**: Images have been pushed to the registry but not deployed. Manual deployment or Heroku release is required." >> $GITHUB_STEP_SUMMARY

  # security-scan:
  #   runs-on: ubuntu-latest
  #   needs: [get-version, build-and-push]
  #   if: always() && needs.build-and-push.result == 'success'
  #   permissions:
  #     contents: read
  #     security-events: write
  #     actions: read  # Required for workflow run access

  #   strategy:
  #     matrix:
  #       service: [agent_service, streamlit_app]

  #   steps:
  #   - name: Log in to Container Registry
  #     uses: docker/login-action@v3
  #     with:
  #       registry: ${{ env.REGISTRY }}
  #       username: ${{ secrets.DOCKER_USERNAME }}
  #       password: ${{ secrets.DOCKER_PASSWORD }}

  #   - name: Run Trivy vulnerability scanner
  #     uses: aquasecurity/trivy-action@master
  #     with:
  #       image-ref: ${{ env.REGISTRY }}/pathforge-ai/${{ matrix.service }}:${{ needs.get-version.outputs.version }}
  #       format: 'sarif'
  #       output: 'trivy-results-${{ matrix.service }}.sarif'

  #   - name: Validate SARIF file
  #     run: |
  #       if [ ! -f "trivy-results-${{ matrix.service }}.sarif" ]; then
  #         echo "Error: SARIF file not found!"
  #         exit 1
  #       fi
  #       echo "SARIF file created successfully for ${{ matrix.service }}"
  #       echo "File size: $(stat -c%s trivy-results-${{ matrix.service }}.sarif) bytes"

  #   - name: Upload Trivy scan results to GitHub Security tab
  #     uses: github/codeql-action/upload-sarif@v3
  #     id: upload-sarif
  #     continue-on-error: true
  #     with:
  #       sarif_file: 'trivy-results-${{ matrix.service }}.sarif'

  #   - name: Upload SARIF as artifact (fallback)
  #     if: steps.upload-sarif.outcome == 'failure'
  #     uses: actions/upload-artifact@v4
  #     with:
  #       name: trivy-sarif-${{ matrix.service }}
  #       path: 'trivy-results-${{ matrix.service }}.sarif'
  #       retention-days: 30

  #   - name: Security scan summary
  #     run: |
  #       echo "## Security Scan Results for ${{ matrix.service }}" >> $GITHUB_STEP_SUMMARY
  #       if [ "${{ steps.upload-sarif.outcome }}" == "success" ]; then
  #         echo "✅ SARIF results uploaded to GitHub Security tab" >> $GITHUB_STEP_SUMMARY
  #       else
  #         echo "⚠️ SARIF upload to Security tab failed - results saved as artifact" >> $GITHUB_STEP_SUMMARY
  #         echo "💡 This may indicate GitHub Advanced Security is not enabled for this repository" >> $GITHUB_STEP_SUMMARY
  #       fi

  #       # Extract and display summary from SARIF file
  #       if command -v jq >/dev/null 2>&1; then
  #         vulnerabilities=$(jq '.runs[0].results | length' trivy-results-${{ matrix.service }}.sarif 2>/dev/null || echo "0")
  #         echo "🔍 Total vulnerabilities found: $vulnerabilities" >> $GITHUB_STEP_SUMMARY
  #       fi
