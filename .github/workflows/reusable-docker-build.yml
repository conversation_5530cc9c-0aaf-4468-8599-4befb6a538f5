name: PathForge AI - Docker Build

on:
  workflow_call:
    inputs:
      push:
        description: 'Push images to registry'
        required: false
        type: boolean
        default: false
      registry:
        description: 'Docker registry'
        required: false
        type: string
        default: ''
      tag-prefix:
        description: 'Tag prefix for images'
        required: false
        type: string
        default: 'latest'
      report-sizes:
        description: 'Report Docker image sizes in summary'
        required: false
        type: boolean
        default: false
      build-agent-service:
        description: 'Build agent service image'
        required: false
        type: boolean
        default: true
      build-backend-service:
        description: 'Build backend service image'
        required: false
        type: boolean
        default: true
      build-streamlit-app:
        description: 'Build streamlit app image'
        required: false
        type: boolean
        default: true
      build-frontend:
        description: 'Build frontend image'
        required: false
        type: boolean
        default: true
    secrets:
      HEROKU_API_KEY:
        required: false
      HEROKU_EMAIL:
        required: false
      # Keep backward compatibility for other Docker registries
      DOCKER_USERNAME:
        required: false
      DOCKER_PASSWORD:
        required: false

jobs:
  # Generate matrix based on input parameters
  generate-matrix:
    runs-on: ubuntu-latest
    outputs:
      matrix: ${{ steps.set-matrix.outputs.matrix }}
    steps:
    - name: Generate build matrix
      id: set-matrix
      run: |
        services=()
        if [ "${{ inputs.build-agent-service }}" = "true" ]; then
          services+=("agent_service")
        fi
        if [ "${{ inputs.build-streamlit-app }}" = "true" ]; then
          services+=("streamlit_app")
        fi
        if [ "${{ inputs.build-frontend }}" = "true" ]; then
          services+=("frontend")
        fi
        if [ "${{ inputs.build-backend-service }}" = "true" ]; then
          services+=("backend")
        fi

        # Convert array to JSON (compact format)
        matrix_json=$(printf '%s\n' "${services[@]}" | jq -R . | jq -s -c .)
        echo "matrix={\"service\":$matrix_json}" >> $GITHUB_OUTPUT
        echo "Generated matrix: {\"service\":$matrix_json}"

  docker-build:
    runs-on: ubuntu-latest
    needs: generate-matrix
    if: needs.generate-matrix.outputs.matrix != '{"service":[]}'
    strategy:
      matrix: ${{ fromJson(needs.generate-matrix.outputs.matrix) }}

    steps:
    - uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      with:
        driver-opts: |
          network=host
          image=moby/buildkit:v0.12.5
        buildkitd-flags: |
          --allow-insecure-entitlement network.host
          --allow-insecure-entitlement security.insecure
        buildkitd-config-inline: |
          [registry."registry.heroku.com"]
            mirrors = ["registry.heroku.com"]
          [worker.oci]
            max-parallelism = 2
          [worker.containerd]
            max-parallelism = 2

    - name: Log in to Container Registry
      if: ${{ inputs.push && inputs.registry }}
      uses: docker/login-action@v3
      with:
        registry: ${{ inputs.registry }}
        username: ${{ startsWith(inputs.registry, 'registry.heroku.com') && secrets.HEROKU_EMAIL || secrets.DOCKER_USERNAME }}
        password: ${{ startsWith(inputs.registry, 'registry.heroku.com') && secrets.HEROKU_API_KEY || secrets.DOCKER_PASSWORD }}

    - name: Alternative Heroku Container Registry Login
      if: ${{ inputs.push && startsWith(inputs.registry, 'registry.heroku.com') }}
      run: |
        echo "${{ secrets.HEROKU_API_KEY }}" | docker login --username=${{ secrets.HEROKU_EMAIL }} --password-stdin registry.heroku.com

    - name: Verify Heroku Container Registry Access
      if: ${{ inputs.push && startsWith(inputs.registry, 'registry.heroku.com') }}
      run: |
        echo "🔍 Verifying Heroku Container Registry access..."
        echo "Registry: ${{ inputs.registry }}"
        echo "Email: ${{ secrets.HEROKU_EMAIL }}"
        echo "App: pathforge-ai"
        echo "Service: ${{ matrix.service }}"
        
        # Verify authentication
        docker info | grep -A 10 "Registry Mirrors:" || true
        
        # Test registry connectivity
        if curl -s -H "Authorization: Bearer ${{ secrets.HEROKU_API_KEY }}" \
             -H "Accept: application/vnd.heroku+json; version=3" \
             "https://api.heroku.com/apps/pathforge-ai" > /dev/null; then
          echo "✅ Heroku API access verified"
        else
          echo "❌ Heroku API access failed"
        fi

    - name: Extract metadata
      if: ${{ inputs.push && inputs.registry }}
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: |
          ${{ startsWith(inputs.registry, 'registry.heroku.com') && 
              format('{0}/{1}/{2}', inputs.registry, 'pathforge-ai',
                matrix.service == 'frontend' && 'web' ||
                matrix.service == 'agent_service' && 'agent' ||
                matrix.service == 'streamlit_app' && 'streamlit' ||
                matrix.service == 'backend' && 'api' ||
                matrix.service) ||
              format('{0}/pathforge-ai/{1}', inputs.registry, matrix.service) }}
        tags: |
          type=raw,value=${{ inputs.tag-prefix }}
          type=raw,value=latest

    - name: Setup dockerignore for build
      run: |
        if [ "${{ matrix.service }}" = "agent_service" ]; then
          cp docker/.dockerignore.service .dockerignore
        elif [ "${{ matrix.service }}" = "frontend" ]; then
          cp docker/.dockerignore.frontend .dockerignore
        else
          cp docker/.dockerignore.app .dockerignore
        fi

    - name: Validate LangChain dependencies
      if: matrix.service == 'streamlit_app'
      run: |
        echo "🔍 Validating LangChain dependencies for streamlit build..."
        # Check for NVIDIA/GPU packages that increase image size
        echo "🔍 Checking for GPU packages that can be removed..."
        pip list | grep -i nvidia || echo "✅ No NVIDIA packages found"
        pip list | grep -i cuda || echo "✅ No CUDA packages found"
        pip list | grep -E "torch|tensorflow" || echo "✅ No heavy ML frameworks found"
        
        # Quick dependency check using pip's dry-run
        python -m pip install --dry-run \
          langchain-core==0.3.64 \
          langchain-openai==0.3.21 \
          langchain-anthropic==0.3.15
        echo "✅ LangChain dependencies validated for CPU-only deployment"

    - name: Optimize dependencies for CPU-only deployment
      if: matrix.service == 'streamlit_app'
      run: |
        echo "🎯 Optimizing for CPU-only deployment..."
        
        # Create optimized Dockerfile for CPU-only build
        cat > docker/Dockerfile.app.cpu-only << 'EOF'
        # CPU-optimized multi-stage build for Streamlit app
        FROM python:3.12.3-slim AS builder

        # Install minimal build dependencies with cache mount
        RUN --mount=type=cache,target=/var/cache/apt,sharing=locked \
            --mount=type=cache,target=/var/lib/apt,sharing=locked \
            apt-get update && apt-get install -y --no-install-recommends \
            gcc \
            g++ \
            libc6-dev \
            libffi-dev \
            libssl-dev \
            build-essential \
            && rm -rf /var/lib/apt/lists/*

        WORKDIR /app

        # Install uv for faster dependency resolution
        RUN --mount=type=cache,target=/root/.cache/pip \
            pip install --no-cache-dir --upgrade pip uv

        # Create virtual environment
        RUN uv venv /opt/venv
        ENV PATH="/opt/venv/bin:$PATH"

        # Install core dependencies first (most stable)
        RUN --mount=type=cache,target=/root/.cache/uv \
            uv pip install \
            httpx==0.27.2 \
            pydantic==2.11.5 \
            python-dotenv==1.1.0 \
            streamlit==1.45.1 \
            pypdf==5.3.1 \
            pandas==2.2.3

        # Install LangChain dependencies (CPU-only)
        RUN --mount=type=cache,target=/root/.cache/uv \
            uv pip install \
            langchain-core==0.3.64 \
            langchain-openai==0.3.21 \
            langchain-anthropic==0.3.15

        RUN --mount=type=cache,target=/root/.cache/uv \
            uv pip install \
            langchain-aws==0.2.23 \
            langchain-community==0.3.24 \
            langchain-google-genai==2.1.4

        RUN --mount=type=cache,target=/root/.cache/uv \
            uv pip install \
            langchain-google-vertexai==2.0.7 \
            langchain-groq==0.2.5 \
            langchain-ollama==0.2.3

        RUN --mount=type=cache,target=/root/.cache/uv \
            uv pip install \
            langgraph==0.3.34 \
            psycopg2-binary==2.9.9 \
            openai==1.81.0

        RUN --mount=type=cache,target=/root/.cache/uv \
            uv pip install \
            pydantic-settings==2.6.1 \
            numexpr==2.10.1 \
            numpy==1.26.4 \
            docx2txt==0.8 \
            pyowm==3.3.0

        # Install ML dependencies with CPU-only versions
        RUN --mount=type=cache,target=/root/.cache/uv \
            uv pip install \
            langchain-chroma \
            sentence-transformers

        # Explicitly remove any NVIDIA/CUDA packages that might have been installed
        RUN pip uninstall -y \
            nvidia-cublas-cu11 \
            nvidia-cublas-cu12 \
            nvidia-cuda-cupti-cu11 \
            nvidia-cuda-cupti-cu12 \
            nvidia-cuda-nvrtc-cu11 \
            nvidia-cuda-nvrtc-cu12 \
            nvidia-cuda-runtime-cu11 \
            nvidia-cuda-runtime-cu12 \
            nvidia-cudnn-cu11 \
            nvidia-cudnn-cu12 \
            nvidia-cufft-cu11 \
            nvidia-cufft-cu12 \
            nvidia-curand-cu11 \
            nvidia-curand-cu12 \
            nvidia-cusolver-cu11 \
            nvidia-cusolver-cu12 \
            nvidia-cusparse-cu11 \
            nvidia-cusparse-cu12 \
            nvidia-nccl-cu11 \
            nvidia-nccl-cu12 \
            nvidia-nvtx-cu11 \
            nvidia-nvtx-cu12 \
            || echo "✅ No NVIDIA packages to remove"

        # Clean up build artifacts aggressively
        RUN rm -rf /tmp/* /var/tmp/* /root/.cache/* /opt/venv/lib/python*/site-packages/*/tests

        # Runtime stage - use same slim base for consistency
        FROM python:3.12.3-slim AS runtime

        # Install only essential runtime dependencies
        RUN --mount=type=cache,target=/var/cache/apt,sharing=locked \
            --mount=type=cache,target=/var/lib/apt,sharing=locked \
            apt-get update && apt-get install -y --no-install-recommends \
            libffi8 \
            libssl3 \
            ca-certificates \
            curl \
            && rm -rf /var/lib/apt/lists/*

        WORKDIR /app

        # Copy virtual environment from builder
        COPY --from=builder /opt/venv /opt/venv
        ENV PATH="/opt/venv/bin:$PATH"

        # Copy source code in order of change frequency (least to most)
        COPY src/schema/ ./schema/
        COPY src/core/ ./core/
        COPY src/client/ ./client/
        COPY src/agents/ ./agents/
        COPY src/streamlit_app.py .

        # Set environment variables for optimization
        ENV PYTHONUNBUFFERED=1
        ENV PYTHONDONTWRITEBYTECODE=1
        ENV PYTHONPATH=/app
        ENV PYTHONOPTIMIZE=2

        # Streamlit optimizations to reduce resource usage
        ENV STREAMLIT_SERVER_HEADLESS=true
        ENV STREAMLIT_SERVER_ENABLE_CORS=false
        ENV STREAMLIT_SERVER_ENABLE_XSRF_PROTECTION=false
        ENV STREAMLIT_BROWSER_GATHER_USAGE_STATS=false
        ENV STREAMLIT_SERVER_MAX_UPLOAD_SIZE=50
        ENV STREAMLIT_SERVER_MAX_MESSAGE_SIZE=50

        # Disable telemetry and analytics to reduce network calls
        ENV ANONYMIZED_TELEMETRY=false
        ENV CHROMA_TELEMETRY_DISABLED=true

        # CPU-only optimization flags
        ENV OMP_NUM_THREADS=1
        ENV OPENBLAS_NUM_THREADS=1
        ENV MKL_NUM_THREADS=1
        ENV VECLIB_MAXIMUM_THREADS=1
        ENV NUMEXPR_NUM_THREADS=1

        # Create non-root user for security
        RUN groupadd -g 1001 appgroup && \
            useradd -u 1001 -g appgroup -m appuser && \
            chown -R appuser:appgroup /app

        USER appuser

        EXPOSE 8501

        # Simplified health check
        HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
            CMD curl -f http://localhost:8501/_stcore/health || exit 1

        # Use exec form for better signal handling
        CMD ["streamlit", "run", "streamlit_app.py", "--server.port=8501", "--server.address=0.0.0.0"]
        EOF
        
        echo "✅ CPU-optimized Dockerfile created"

    - name: Free up disk space for large builds
      if: matrix.service == 'streamlit_app'
      run: |
        echo "🧹 Freeing up disk space for streamlit build..."
        sudo rm -rf /usr/share/dotnet
        sudo rm -rf /opt/ghc
        sudo rm -rf /usr/local/share/boost
        sudo rm -rf "$AGENT_TOOLSDIRECTORY"
        docker system prune -af
        df -h

    - name: Build and push Docker image
      timeout-minutes: ${{ matrix.service == 'streamlit_app' && 45 || 30 }}
      uses: docker/build-push-action@v6
      with:
        context: ${{ matrix.service == 'backend' && '.' || '.' }}
        file: |
          ${{ matrix.service == 'backend' && 'docker/Dockerfile.backend' ||
             (matrix.service == 'agent_service' && 'docker/Dockerfile.agent') ||
             (matrix.service == 'frontend' && 'docker/Dockerfile.frontend') ||
             (matrix.service == 'streamlit_app' && 'docker/Dockerfile.streamlit') ||
             'docker/Dockerfile.agent' }}
        push: ${{ inputs.push }}
        tags: ${{ inputs.push && inputs.registry && steps.meta.outputs.tags || format('{0}:latest', matrix.service) }}
        labels: ${{ inputs.push && inputs.registry && steps.meta.outputs.labels || '' }}
        platforms: linux/amd64
        provenance: false
        cache-from: |
          type=gha
        cache-to: |
          type=gha,mode=max,ignore-error=true
        build-args: |
          BUILDKIT_INLINE_CACHE=1
        shm-size: 2g

    - name: Fallback Heroku Push (if buildx push fails)
      if: ${{ failure() && inputs.push && startsWith(inputs.registry, 'registry.heroku.com') }}
      run: |
        echo "🔄 Attempting fallback push to Heroku Container Registry..."
        
        # Determine process type for Heroku
        PROCESS_TYPE="${{ matrix.service == 'frontend' && 'web' || matrix.service == 'agent_service' && 'agent' || matrix.service == 'streamlit_app' && 'streamlit' || matrix.service == 'backend' && 'api' || matrix.service }}"
        BASE_IMAGE_NAME="${{ inputs.registry }}/pathforge-ai/$PROCESS_TYPE"
        VERSION_TAG="$BASE_IMAGE_NAME:${{ inputs.tag-prefix }}"
        LATEST_TAG="$BASE_IMAGE_NAME:latest"
        
        echo "📦 Building image with tags:"
        echo "  Version tag: $VERSION_TAG"
        echo "  Latest tag: $LATEST_TAG"
        
        # Build image locally with both tags
        docker build \
          -f ${{ matrix.service == 'backend' && 'docker/Dockerfile.backend' || (matrix.service == 'agent_service' && 'docker/Dockerfile.agent') || (matrix.service == 'frontend' && 'docker/Dockerfile.frontend') || (matrix.service == 'streamlit_app' && 'docker/Dockerfile.streamlit') || 'docker/Dockerfile.agent' }} \
          -t "$VERSION_TAG" \
          -t "$LATEST_TAG" \
          ${{ matrix.service == 'backend' && '.' || '.' }}
        
        # Push version tag with retry logic
        echo "🚀 Pushing version tag: $VERSION_TAG"
        for i in {1..3}; do
          echo "Version push attempt $i/3..."
          if docker push "$VERSION_TAG"; then
            echo "✅ Successfully pushed $VERSION_TAG"
            break
          else
            echo "❌ Version push attempt $i failed"
            if [ $i -eq 3 ]; then
              echo "❌ All version push attempts failed"
              exit 1
            fi
            sleep 5
          fi
        done
        
        # Push latest tag with retry logic
        echo "🚀 Pushing latest tag: $LATEST_TAG"
        for i in {1..3}; do
          echo "Latest push attempt $i/3..."
          if docker push "$LATEST_TAG"; then
            echo "✅ Successfully pushed $LATEST_TAG"
            break
          else
            echo "❌ Latest push attempt $i failed"
            if [ $i -eq 3 ]; then
              echo "❌ All latest push attempts failed"
              exit 1
            fi
            sleep 5
          fi
        done
        
        echo "🎉 Successfully pushed both version (${{ inputs.tag-prefix }}) and latest tags!"

    - name: Validate CPU-only functionality
      if: matrix.service == 'streamlit_app'
      run: |
        echo "🧪 Testing Streamlit app functionality without GPU packages..."
        # Quick smoke test (adjust based on your app structure)
        python -c "
        import streamlit as st
        import langchain_core
        import langchain_openai
        import langchain_anthropic
        print('✅ All core dependencies imported successfully')
        print('✅ CPU-only build ready for deployment')
        "

    - name: Validate NVIDIA package removal
      if: matrix.service == 'streamlit_app'
      run: |
        echo "🔍 Validating NVIDIA packages were removed from built image..."
        IMAGE_TAG="${{ inputs.push && inputs.registry && steps.meta.outputs.tags || format('{0}:latest', matrix.service) }}"
        
        # Extract first tag if multiple tags
        FIRST_TAG=$(echo "$IMAGE_TAG" | head -n 1)
        
        echo "Checking image: $FIRST_TAG"
        
        # Check for NVIDIA packages in the built image
        if docker run --rm "$FIRST_TAG" pip list | grep -i nvidia; then
          echo "⚠️ NVIDIA packages still present in image"
          docker run --rm "$FIRST_TAG" pip list | grep -i nvidia
        else
          echo "✅ No NVIDIA packages found in image"
        fi
        
        # Check for CUDA packages
        if docker run --rm "$FIRST_TAG" pip list | grep -i cuda; then
          echo "⚠️ CUDA packages still present in image"
          docker run --rm "$FIRST_TAG" pip list | grep -i cuda
        else
          echo "✅ No CUDA packages found in image"
        fi
        
        # Report image size
        IMAGE_SIZE=$(docker images "$FIRST_TAG" --format 'table {{.Size}}' | tail -n 1)
        echo "📊 Final image size: $IMAGE_SIZE"

    - name: Report image size and optimization info
      if: ${{ inputs.report-sizes }}
      run: |
        IMAGE_TAG="${{ inputs.push && inputs.registry && steps.meta.outputs.tags || format('{0}:latest', matrix.service) }}"
        IMAGE_SIZE=$(docker images $IMAGE_TAG --format 'table {{.Size}}' | tail -n 1)
        IMAGE_SIZE_BYTES=$(docker images $IMAGE_TAG --format 'table {{.Size}}' | tail -n 1 | sed 's/[^0-9.]//g')

        # Get image details for optimization info
        IMAGE_LAYERS=$(docker history $IMAGE_TAG --no-trunc --format "table {{.Size}}" | grep -v SIZE | wc -l)

        # Create or append to size summary with optimization details
        if [ "${{ matrix.service }}" = "agent_service" ]; then
          echo "## 📦 Docker Image Sizes & Optimization" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "| Service | Size | Layers | Optimization |" >> $GITHUB_STEP_SUMMARY
          echo "|---------|------|--------|-------------|" >> $GITHUB_STEP_SUMMARY
          echo "| Agent Service | $IMAGE_SIZE | $IMAGE_LAYERS | Multi-stage Alpine build |" >> $GITHUB_STEP_SUMMARY
        elif [ "${{ matrix.service }}" = "streamlit_app" ]; then
          echo "| Streamlit App | $IMAGE_SIZE | $IMAGE_LAYERS | Optimized Alpine + minimal deps |" >> $GITHUB_STEP_SUMMARY
        elif [ "${{ matrix.service }}" = "backend" ]; then
          echo "| Backend Service | $IMAGE_SIZE | $IMAGE_LAYERS | Node.js Alpine with Prisma |" >> $GITHUB_STEP_SUMMARY # Example description
        else
          echo "| Frontend | $IMAGE_SIZE | $IMAGE_LAYERS | Multi-stage Nginx + Node Alpine |" >> $GITHUB_STEP_SUMMARY
        fi

    - name: Verify both tags were pushed
      if: ${{ inputs.push && inputs.registry }}
      run: |
        # Determine process type for Heroku
        PROCESS_TYPE="${{ matrix.service == 'frontend' && 'web' || matrix.service == 'agent_service' && 'agent' || matrix.service == 'streamlit_app' && 'streamlit' || matrix.service == 'backend' && 'api' || matrix.service }}"
        VERSION_IMAGE="${{ inputs.registry }}/pathforge-ai/$PROCESS_TYPE:${{ inputs.tag-prefix }}"
        LATEST_IMAGE="${{ inputs.registry }}/pathforge-ai/$PROCESS_TYPE:latest"
        
        echo "🔍 Verifying that both tags were pushed successfully..."
        
        # Check if images exist in registry (for Heroku, we can't easily inspect, so we'll check locally)
        if docker images "$VERSION_IMAGE" --format 'table {{.Repository}}:{{.Tag}}' | grep -q "${{ inputs.tag-prefix }}"; then
          echo "✅ Version tag (${{ inputs.tag-prefix }}) verified locally"
        else
          echo "⚠️ Version tag (${{ inputs.tag-prefix }}) not found locally"
        fi
        
        if docker images "$LATEST_IMAGE" --format 'table {{.Repository}}:{{.Tag}}' | grep -q "latest"; then
          echo "✅ Latest tag verified locally"
        else
          echo "⚠️ Latest tag not found locally"
        fi
        
        echo "📝 Tags pushed for ${{ matrix.service }}:"
        echo "  - $VERSION_IMAGE"
        echo "  - $LATEST_IMAGE"

    - name: Add Docker build summary
      run: |
        if [ "${{ inputs.push }}" = "true" ] && [ -n "${{ inputs.registry }}" ]; then
          # Determine process type for Heroku
          PROCESS_TYPE="${{ matrix.service == 'frontend' && 'web' || matrix.service == 'agent_service' && 'agent' || matrix.service == 'streamlit_app' && 'streamlit' || matrix.service == 'backend' && 'api' || matrix.service }}"
          VERSION_IMAGE="${{ inputs.registry }}/pathforge-ai/$PROCESS_TYPE:${{ inputs.tag-prefix }}"
          LATEST_IMAGE="${{ inputs.registry }}/pathforge-ai/$PROCESS_TYPE:latest"
          
          echo "## 🐳 Docker Build Summary - ${{ matrix.service }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 📦 Pushed Images:" >> $GITHUB_STEP_SUMMARY
          echo "- **Version Tag:** \`$VERSION_IMAGE\`" >> $GITHUB_STEP_SUMMARY
          echo "- **Latest Tag:** \`$LATEST_IMAGE\`" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 🔧 Build Details:" >> $GITHUB_STEP_SUMMARY
          echo "- **Registry:** ${{ inputs.registry }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Service:** ${{ matrix.service }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Version:** ${{ inputs.tag-prefix }}" >> $GITHUB_STEP_SUMMARY
          if [ "${{ matrix.service }}" = "streamlit_app" ]; then
            echo "- **Optimization:** Multi-stage Alpine build with minimal dependencies" >> $GITHUB_STEP_SUMMARY
          elif [ "${{ matrix.service }}" = "frontend" ]; then
            echo "- **Optimization:** Multi-stage Node Alpine + Nginx with static build" >> $GITHUB_STEP_SUMMARY
          elif [ "${{ matrix.service }}" = "backend" ]; then
            echo "- **Optimization:** Node.js Alpine build with Prisma client generation" >> $GITHUB_STEP_SUMMARY
          elif [ "${{ matrix.service }}" = "agent_service" ]; then
            echo "- **Optimization:** Multi-stage Python Alpine build with optimized dependencies" >> $GITHUB_STEP_SUMMARY
          fi
          echo "" >> $GITHUB_STEP_SUMMARY
        else
          echo "## 🐳 Docker Build Summary - ${{ matrix.service }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Local Image:** \`${{ matrix.service }}:latest\`" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "- **Service:** ${{ matrix.service }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Tag:** latest" >> $GITHUB_STEP_SUMMARY
          echo "- **Status:** Built locally (not pushed)" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
        fi
