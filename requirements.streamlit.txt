# Streamlit requirements - includes all dependencies needed by the Streamlit app
# Core web framework
streamlit==1.45.1

# HTTP client for agent communication
httpx==0.27.2

# Data validation and serialization
pydantic==2.11.5

# Environment variable management
python-dotenv==1.1.0

# Data processing (needed for streamlit file uploads)
pandas==2.2.3

# PDF processing (for CV extraction feature)
pypdf==5.3.1

# Core LangGraph and LangChain - let dependency resolver handle versions
langgraph>=0.3.0
langchain>=0.3.0
langchain-core>=0.3.0
langchain-community>=0.3.0
langchain-anthropic>=0.3.0
langchain-openai>=0.3.0
langchain-groq>=0.2.0
langchain-google-genai>=2.0.0
langchain-google-vertexai>=2.0.7
langchain-aws>=0.2.0
langchain-chroma>=0.2.0
langchain-ollama>=0.2.3
langchain-text-splitters>=0.3.0

# LangGraph checkpointing and SDK (needed by agents module)
langgraph-checkpoint>=2.0.0
langgraph-checkpoint-sqlite>=2.0.0
langgraph-prebuilt>=0.3.0
langgraph-sdk>=0.1.0

# LangSmith for tracing
langsmith>=0.1.0

# AI model clients - use compatible versions
openai>=1.68.0
anthropic>=0.50.0
groq>=0.25.0

# Database support
aiosqlite>=0.20.0
sqlalchemy>=2.0.0

# Vector database
chromadb>=0.6.0

# Document processing (needed for CV extraction and tools)
docx2txt>=0.8

# Embeddings support (needed for HuggingFaceEmbeddings in tools.py)
sentence-transformers>=2.0.0

# Token counting (needed for OpenAI models)
tiktoken>=0.8.0

# Additional utilities
tenacity>=9.0.0
typing-extensions>=4.12.0
pyyaml>=6.0.0
requests>=2.31.0
