#!/bin/bash

# Script to validate LangChain dependency compatibility

echo "🔍 Validating LangChain dependency compatibility..."

# Check if uv is available for faster resolution
if command -v uv &> /dev/null; then
    echo "✅ UV package manager detected, using for validation"
    INSTALLER="uv pip install --dry-run"
else
    echo "⚠️  UV not detected, using pip for validation"
    INSTALLER="pip install --dry-run"
fi

# Create temporary virtual environment for testing
python -m venv /tmp/langchain-test
source /tmp/langchain-test/bin/activate

echo "📦 Testing core LangChain packages compatibility..."

# Test the exact versions from Dockerfile.app
$INSTALLER \
    langchain-core==0.3.64 \
    langchain-openai==0.3.21 \
    langchain-anthropic==0.3.15 \
    langchain-aws==0.2.23 \
    langchain-community==0.3.24 \
    langchain-google-genai==2.1.4 \
    langchain-google-vertexai==2.0.7 \
    langchain-groq==0.2.5 \
    langchain-ollama==0.2.3 \
    langgraph==0.3.34

if [ $? -eq 0 ]; then
    echo "✅ All LangChain dependencies are compatible!"
else
    echo "❌ Dependency conflict detected!"
    echo "💡 Please check the latest compatible versions at:"
    echo "   https://python.langchain.com/docs/versions/v0_3/"
    exit 1
fi

# Cleanup
deactivate
rm -rf /tmp/langchain-test

echo "🎉 Dependency validation completed successfully!" 