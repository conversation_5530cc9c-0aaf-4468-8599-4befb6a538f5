#!/bin/bash

# PathForge AI - Local Development Runner
# This script helps you run the PathForge AI stack locally using Docker Desktop

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker Desktop first."
        exit 1
    fi
    print_success "Docker is running"
}

# Check if required files exist
check_requirements() {
    if [ ! -f "docker-compose.local.yml" ]; then
        print_error "docker-compose.local.yml not found. Make sure you're in the project root directory."
        exit 1
    fi
    
    if [ ! -f ".env" ]; then
        print_warning ".env file not found. Checking for .env.local.example..."
        if [ -f ".env.local.example" ]; then
            print_status "Copying .env.local.example to .env"
            cp .env.local.example .env
            print_warning "Please update .env with your actual API keys before running the services."
        else
            print_error ".env.local.example not found. Please create a .env file with your configuration."
            exit 1
        fi
    fi
}

# Function to pull images
pull_images() {
    print_status "Pulling latest images..."
    VERSION=${VERSION:-latest}
    
    # Pull all service images
    docker pull registry.heroku.com/pathforge-ai/agent:${VERSION} || print_warning "Failed to pull agent image"
    docker pull registry.heroku.com/pathforge-ai/streamlit:${VERSION} || print_warning "Failed to pull streamlit image"
    docker pull registry.heroku.com/pathforge-ai/web:${VERSION} || print_warning "Failed to pull frontend image"
    docker pull registry.heroku.com/pathforge-ai/api:${VERSION} || print_warning "Failed to pull backend image"
    
    print_success "Image pull completed"
}

# Function to start services
start_services() {
    print_status "Starting PathForge AI services..."
    
    # Create network if it doesn't exist
    docker network create pathforge_network 2>/dev/null || true
    
    # Start services with docker-compose
    docker-compose -f docker-compose.local.yml up -d
    
    print_success "Services started successfully!"
    echo ""
    print_status "Service URLs:"
    echo "  📊 Streamlit App:    http://localhost:8501"
    echo "  🌐 Frontend:         http://localhost:3000"
    echo "  🔧 Backend API:      http://localhost:8080"
    echo "  🤖 Agent Service:    http://localhost:8000"
    echo ""
    print_status "Use 'docker-compose -f docker-compose.local.yml logs -f' to view logs"
    print_status "Use '$0 stop' to stop all services"
}

# Function to stop services
stop_services() {
    print_status "Stopping PathForge AI services..."
    docker-compose -f docker-compose.local.yml down
    print_success "Services stopped successfully!"
}

# Function to show logs
show_logs() {
    if [ -n "$2" ]; then
        print_status "Showing logs for service: $2"
        docker-compose -f docker-compose.local.yml logs -f "$2"
    else
        print_status "Showing logs for all services..."
        docker-compose -f docker-compose.local.yml logs -f
    fi
}

# Function to show status
show_status() {
    print_status "Service status:"
    docker-compose -f docker-compose.local.yml ps
}

# Function to restart services
restart_services() {
    print_status "Restarting PathForge AI services..."
    docker-compose -f docker-compose.local.yml restart
    print_success "Services restarted successfully!"
}

# Function to show help
show_help() {
    echo "PathForge AI - Local Development Runner"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start       Start all services (default)"
    echo "  stop        Stop all services"
    echo "  restart     Restart all services"
    echo "  status      Show service status"
    echo "  logs        Show logs for all services"
    echo "  logs [svc]  Show logs for specific service"
    echo "  pull        Pull latest images"
    echo "  help        Show this help message"
    echo ""
    echo "Environment Variables:"
    echo "  VERSION     Image version tag (default: latest)"
    echo ""
    echo "Examples:"
    echo "  $0 start                    # Start all services"
    echo "  $0 logs pathforge_ai_agent_service   # Show agent logs"
    echo "  VERSION=v1.2.3 $0 start    # Start with specific version"
}

# Main execution
main() {
    local command=${1:-start}
    
    case $command in
        "start")
            check_docker
            check_requirements
            start_services
            ;;
        "stop")
            check_docker
            stop_services
            ;;
        "restart")
            check_docker
            restart_services
            ;;
        "status")
            check_docker
            show_status
            ;;
        "logs")
            check_docker
            show_logs "$@"
            ;;
        "pull")
            check_docker
            pull_images
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "Unknown command: $command"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
