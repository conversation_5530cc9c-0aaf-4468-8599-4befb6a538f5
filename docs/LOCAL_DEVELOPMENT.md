# PathForge AI - Local Development Setup

This guide helps you run the complete PathForge AI stack locally using Docker Desktop.

## 🚀 Quick Start

1. **Prerequisites**
   - Docker Desktop installed and running
   - Git (to clone the repository)
   - 8GB+ RAM recommended

2. **Setup**
   ```bash
   # Clone the repository (if not already done)
   git clone <repository-url>
   cd codepluse-platform
   
   # Copy environment template
   cp .env.local.example .env
   
   # Edit .env with your API keys
   nano .env  # or use your preferred editor
   ```

3. **Run the stack**
   ```bash
   # Make script executable (first time only)
   chmod +x scripts/run-local.sh
   
   # Start all services
   ./scripts/run-local.sh start
   ```

4. **Access the applications**
   - **Streamlit App**: http://localhost:8501 (Main chat interface)
   - **Frontend**: http://localhost:3000 (React web app)
   - **Backend API**: http://localhost:8080 (REST API)
   - **Agent Service**: http://localhost:8000 (AI agent endpoint)

## 📋 Available Commands

```bash
# Service management
./scripts/run-local.sh start      # Start all services
./scripts/run-local.sh stop       # Stop all services
./scripts/run-local.sh restart    # Restart all services
./scripts/run-local.sh status     # Check service status

# Monitoring
./scripts/run-local.sh logs       # View all logs
./scripts/run-local.sh logs pathforge_ai_streamlit_app  # View specific service logs

# Images
./scripts/run-local.sh pull       # Pull latest images
```

## 🔧 Service Configuration

### Image Versions
By default, the local setup uses the `latest` tag. To use a specific version:

```bash
VERSION=v1.2.3 ./scripts/run-local.sh start
```

### Port Mapping
| Service | Internal Port | External Port | URL |
|---------|---------------|---------------|-----|
| Streamlit App | 8501 | 8501 | http://localhost:8501 |
| Frontend | 8080 | 3000 | http://localhost:3000 |
| Backend API | 8080 | 8080 | http://localhost:8080 |
| Agent Service | 8000 | 8000 | http://localhost:8000 |

### Environment Variables

#### Required API Keys
- `OPENAI_API_KEY` - For OpenAI models
- `ANTHROPIC_API_KEY` - For Claude models

#### Optional API Keys
- `LANGSMITH_API_KEY` - For LangSmith tracing
- `OPENROUTER_API_KEY` - For OpenRouter models (fallback provided)
- `BRAVE_SEARCH_API_KEY` - For web search (fallback provided)
- `OPENWEATHERMAP_API_KEY` - For weather data (fallback provided)

#### Database Configuration
The setup uses Supabase by default. Database credentials are pre-configured in the template, but you can override them:

```bash
POSTGRES_HOST=your-database-host
POSTGRES_USER=your-username
POSTGRES_PASSWORD=your-password
POSTGRES_DB=your-database-name
POSTGRES_PORT=5432
```

## 🏗️ Architecture Overview

```mermaid
graph TD
    A[User] -->|Port 8501| B[Streamlit App]
    A -->|Port 3000| C[Frontend React App]
    C -->|Port 8080| D[Backend API]
    B -->|Port 8000| E[Agent Service]
    D --> F[Database Supabase]
    E --> F
    E --> G[External APIs]
```

## 🐛 Troubleshooting

### Common Issues

#### Docker not running
```bash
# Check Docker status
docker info

# Start Docker Desktop and try again
```

#### Port conflicts
If ports are already in use, you can modify the `docker-compose.local.yml` file:

```yaml
services:
  pathforge_ai_streamlit_app:
    ports:
      - "8502:8501"  # Change external port
```

#### Service startup failures
```bash
# Check service logs
./scripts/run-local.sh logs [service-name]

# Check service health
./scripts/run-local.sh status
```

#### Image pull failures
```bash
# Pull images manually
docker pull registry.heroku.com/pathforge-ai/agent:latest
docker pull registry.heroku.com/pathforge-ai/streamlit:latest
docker pull registry.heroku.com/pathforge-ai/web:latest
docker pull registry.heroku.com/pathforge-ai/api:latest
```

### Service Dependencies

The services start in this order to handle dependencies:
1. **Backend API** (database connection)
2. **Agent Service** (API dependencies)
3. **Streamlit App** (waits for Agent Service)
4. **Frontend** (waits for Backend API)

### Health Checks

All services include health checks that verify:
- Service is responding on the correct port
- Dependencies are available
- Basic functionality is working

## 📊 Monitoring & Logs

### View all logs
```bash
docker-compose -f docker-compose.local.yml logs -f
```

### View specific service logs
```bash
docker-compose -f docker-compose.local.yml logs -f pathforge_ai_streamlit_app
```

### Service metrics
```bash
# Resource usage
docker stats

# Service status
docker-compose -f docker-compose.local.yml ps
```

## 🔄 Development Workflow

### Using local images
If you want to build and use local images instead of pulling from the registry:

```bash
# Build specific service locally
docker build -f docker/Dockerfile.app -t registry.heroku.com/pathforge-ai/streamlit:latest .

# Or build all services
./scripts/test_docker_build.sh
```

### Hot reload development
For active development, you might want to mount local source code:

```yaml
# Add to docker-compose.local.yml
services:
  pathforge_ai_streamlit_app:
    volumes:
      - ./src:/app/src
```

### Environment switching
```bash
# Development
NODE_ENV=development ./scripts/run-local.sh start

# Production-like
NODE_ENV=production ./scripts/run-local.sh start
```

## 📝 Configuration Files

- `docker-compose.local.yml` - Main compose file for local development
- `.env` - Environment variables (create from `.env.local.example`)
- `scripts/run-local.sh` - Convenience script for managing the stack

## 🔗 Related Documentation

- [Production Deployment](./docs/devops/deployment.md)
- [Docker Swarm Setup](./docker/docker-compose.swarm.yml)
- [CI/CD Pipeline](./.github/workflows/cd.yml)
- [API Documentation](./docs/api/)

## 💡 Tips

1. **Resource allocation**: Ensure Docker Desktop has at least 4GB RAM allocated
2. **Network isolation**: Services communicate through the `pathforge_network` bridge
3. **Data persistence**: Database data is stored externally (Supabase), so no local persistence needed
4. **API rate limits**: Be mindful of API usage when testing locally

## 🆘 Getting Help

If you encounter issues:

1. Check the troubleshooting section above
2. Review service logs: `./scripts/run-local.sh logs`
3. Verify your `.env` configuration
4. Ensure all required API keys are set
5. Check Docker Desktop resource allocation

For development questions, refer to the main project documentation or create an issue in the repository.
