 To do so, set COMPOSE_BAKE=true.
[+] Building 174.4s (83/92)                                                         docker:desktop-linux
 => [pathforge_ai_frontend production 1/6] FROM docker.io/library/nginx:alpine@sha256:65645c7bb6a  38.9s
 => => sha256:9994ea1088e3f1d0eb3dea855f32e7e63742b2644c8611c124ba81bc3453047e 10.49MB / 16.03MB   38.9s
 => => extracting sha256:6e771e15690e2fabf2332d3a3b744495411d6e0b00b2aea64419b58b0066cf81           0.1s
 => => extracting sha256:c60e446e49a0b607fd79968afdc54cedce67644990a3930925add6caf577779d           0.0s
 => => extracting sha256:e6557c42ebeaea010d8a8883fdacdc5a17dea1221416d0d980e206dd42dc7e29           0.0s
 => => extracting sha256:d3282d7e6b7633cf153dce0ca1c72b6ea574edb0b34351f848fb16b7d13c851f           0.0s
 => => extracting sha256:a4ce1202d74643d4e4ce15787afc2402a18802e3d7bfd0f6bf60c912b57eec1f           0.0s
 => => extracting sha256:1ab010a063387e697fc32bb43022532c0e275d2e51fb65c2ac541e082e610a33           0.0s
 => => extracting sha256:37aca2470cdaf48d251d9308c09dc8735b45ebcbefada46537c9989991b3fe6d           0.0s
 => [pathforge_ai_frontend internal] load build context                                 [+] Building 299.9s (85/92)                                        docker:desktop-linux
 => [pathforge_ai_backend internal] load build definition from Dockerfile          0.0s 
 => => transferring dockerfile: 671B                                               0.0s
 => [pathforge_ai_agent_service internal] load build definition from Dockerfile.s  0.0s 
 => => transferring dockerfile: 5.11kB                                             0.0s
 => [pathforge_ai_streamlit_app internal] load metadata for docker.io/library/pyt  3.4s 
 => [pathforge_ai_frontend internal] load metadata for docker.io/library/node:20-  6.0s
 => [pathforge_ai_agent_service auth] library/python:pull token for registry-1.do  0.0s 
 => [pathforge_ai_backend auth] library/node:pull token for registry-1.docker.io   0.0s
 => [pathforge_ai_agent_service internal] load .dockerignore                       0.0s 
 => => transferring context: 2B                                                    0.0s
 => [pathforge_ai_agent_service internal] load build context                       0.0s 
 => => transferring context: 13.05kB                                               0.0s
 => [pathforge_ai_streamlit_app builder 1/8] FROM docker.io/library/python:3.12.3  0.0si
 => CACHED [pathforge_ai_agent_service runtime  2/17] RUN apt-get update && apt-g  0.0s
 => CACHED [pathforge_ai_agent_service runtime  3/17] WORKDIR /app                 0.0s.
 => CACHED [pathforge_ai_agent_service builder  2/14] RUN apt-get update && apt-g  0.0s
 => CACHED [pathforge_ai_agent_service builder  3/14] WORKDIR /app                 0.0s 
 => CACHED [pathforge_ai_agent_service builder  4/14] RUN pip install --no-cache-  0.0s
 => CACHED [pathforge_ai_agent_service builder  5/14] COPY pyproject.toml .        0.0s 
 => CACHED [pathforge_ai_agent_service builder  6/14] COPY uv.lock .               0.0s
 => CACHED [pathforge_ai_agent_service builder  7/14] RUN uv sync --frozen || (ec  0.0s
 => CACHED [pathforge_ai_agent_service builder  8/14] RUN pg_config --version ||   0.0s
 => CACHED [pathforge_ai_agent_service builder  9/14] RUN uv pip list | grep -E "  0.0s
 => CACHED [pathforge_ai_agent_service builder 10/14] RUN uv pip install --force-  0.0s
 => CACHED [pathforge_ai_agent_service builder 11/14] RUN /app/.venv/bin/python -  0.0s
 => CACHED [pathforge_ai_agent_service builder 12/14] RUN /app/.venv/bin/python -  0.0s
 => CACHED [pathforge_ai_agent_service builder 13/14] RUN /app/.venv/bin/python -  0.0s
 => CACHED [pathforge_ai_agent_service builder 14/14] RUN uv cache clean &&     r  0.0s
 => CACHED [pathforge_ai_agent_service runtime  4/17] COPY --from=builder /app/.v  0.0s
 => CACHED [pathforge_ai_agent_service runtime  5/17] COPY src/agents/ ./agents/   0.0s
 => CACHED [pathforge_ai_agent_service runtime  6/17] COPY src/core/ ./core/       0.0s
 => CACHED [pathforge_ai_agent_service runtime  7/17] COPY src/memory/ ./memory/   0.0s
 => CACHED [pathforge_ai_agent_service runtime  8/17] COPY src/schema/ ./schema/   0.0s
 => CACHED [pathforge_ai_agent_service runtime  9/17] COPY src/service/ ./service  0.0s
 => CACHED [pathforge_ai_agent_service runtime 10/17] COPY src/run_service.py .    0.0s
 => CACHED [pathforge_ai_agent_service runtime 11/17] COPY docker/start_service.p  0.0s
 => CACHED [pathforge_ai_agent_service runtime 12/17] COPY docker/core_init_patch  0.0s
 => CACHED [pathforge_ai_agent_service runtime 13/17] RUN touch .env               0.0s
 => CACHED [pathforge_ai_agent_service runtime 14/17] RUN chmod +x start_service.  0.0s
 => CACHED [pathforge_ai_agent_service runtime 15/17] RUN find . -type d -name __  0.0s
 => CACHED [pathforge_ai_agent_service runtime 16/17] RUN groupadd --gid 1001 app  0.0s
 => CACHED [pathforge_ai_agent_service runtime 17/17] RUN chown -R appuser:appgro  0.0s
 => [pathforge_ai_agent_service] exporting to image                                0.0s
 => => exporting layers                                                            0.0s
 => => writing image sha256:6ddf26e5cf22e41b090146bddd08eaf6f75a6f4b7a22f0c7383a4  0.0s
 => => naming to docker.io/library/codepluse-platform-pathforge_ai_agent_service   0.0s
 => [pathforge_ai_agent_service] resolving provenance for metadata file            0.0s
 => [pathforge_ai_streamlit_app internal] load build definition from Dockerfile.a  0.0s
 => => transferring dockerfile: 3.16kB                                             0.0s
 => [pathforge_ai_streamlit_app internal] load .dockerignore                       0.0s
 => => transferring context: 2B                                                    0.0s
 => [pathforge_ai_streamlit_app internal] load build context                       0.0s
 => => transferring context: 62.67kB                                               0.0s
 => CACHED [pathforge_ai_streamlit_app runtime  2/11] RUN apt-get update && apt-g  0.0s
 => CACHED [pathforge_ai_streamlit_app runtime  3/11] WORKDIR /app                 0.0s
 => CACHED [pathforge_ai_streamlit_app builder 2/8] RUN apt-get update && apt-get  0.0s
 => CACHED [pathforge_ai_streamlit_app builder 3/8] WORKDIR /app                   0.0s
 => CACHED [pathforge_ai_streamlit_app builder 4/8] RUN pip install --no-cache-di  0.0s
 => CACHED [pathforge_ai_streamlit_app builder 5/8] RUN pip install --no-cache-di  0.0s
 => CACHED [pathforge_ai_streamlit_app builder 6/8] RUN uv venv /opt/venv          0.0s
 => CACHED [pathforge_ai_streamlit_app builder 7/8] RUN uv pip install --only-bin  0.0s
 => CACHED [pathforge_ai_streamlit_app builder 8/8] RUN pip cache purge &&     rm  0.0s
 => CACHED [pathforge_ai_streamlit_app runtime  4/11] COPY --from=builder /opt/ve  0.0s
 => CACHED [pathforge_ai_streamlit_app runtime  5/11] COPY src/client/ ./client/   0.0s
 => CACHED [pathforge_ai_streamlit_app runtime  6/11] COPY src/schema/ ./schema/   0.0s
 => [pathforge_ai_streamlit_app runtime  7/11] COPY src/agents/ ./agents/          0.0s
 => [pathforge_ai_streamlit_app runtime  8/11] COPY src/core/ ./core/              0.0s
 => [pathforge_ai_streamlit_app runtime  9/11] COPY src/streamlit_app.py .         0.0s
 => [pathforge_ai_streamlit_app runtime 10/11] RUN groupadd -g 1001 appgroup &&    0.2s
 => [pathforge_ai_streamlit_app runtime 11/11] RUN chown -R appuser:appgroup /app  0.2s
 => [pathforge_ai_streamlit_app] exporting to image                                0.0s
 => => exporting layers                                                            0.0s
 => => writing image sha256:cc61ed749d0135cc397b0077cac49081fa1cc7cbb94dc3274aea2  0.0s
 => => naming to docker.io/library/codepluse-platform-pathforge_ai_streamlit_app   0.0s
 => [pathforge_ai_streamlit_app] resolving provenance for metadata file            0.0s
 => [pathforge_ai_backend internal] load .dockerignore                             0.0s
 => => transferring context: 2B                                                    0.0s
 => [pathforge_ai_frontend builder 1/7] FROM docker.io/library/node:20-alpine@sh  46.5s
 => => resolve docker.io/library/node:20-alpine@sha256:d3507a213936fe4ef54760a186  0.0s
 => => sha256:d3507a213936fe4ef54760a186e113db5188472d9efdf491686 7.67kB / 7.67kB  0.0s
 => => sha256:bcab863cb36ee45ce7ecbae8f732b8da6e28cc7267606010547 1.72kB / 1.72kB  0.0s
 => => sha256:452cdbae2e5e353f480899878352a644edd75fc20b497ee219a 6.23kB / 6.23kB  0.0s
 => => sha256:d69d4d41cfe2ee680d6972795e2a1eb9e4dc4ec3b3c5e0797c9 4.14MB / 4.14MB  6.5s
 => => sha256:dfbbeb3612d144440035563f7fe398ae888070848246b5c0 42.63MB / 42.63MB  45.1s
 => => sha256:1c5175aa30154b40b4a72f3074abb4578fa79ff0c9bc8decca 1.26MB / 1.26MB  13.8s
 => => extracting sha256:d69d4d41cfe2ee680d6972795e2a1eb9e4dc4ec3b3c5e0797c9ab43b  0.1s
 => => sha256:ff50adc0fe7c8d4958aaac899545f673cb19402a96d13272932d4dc 444B / 444B  7.1s
 => => extracting sha256:dfbbeb3612d144440035563f7fe398ae888070848246b5c0cb14a401  0.7s
 => => extracting sha256:1c5175aa30154b40b4a72f3074abb4578fa79ff0c9bc8deccac7c142  0.0s
 => => extracting sha256:ff50adc0fe7c8d4958aaac899545f673cb19402a96d13272932d4dc6  0.0s
 => [pathforge_ai_backend internal] load build context                             1.8s
 => => transferring context: 205.60MB                                              1.8s
 => CACHED [pathforge_ai_frontend builder 2/7] WORKDIR /app                        0.3s
 => [pathforge_ai_backend  3/10] RUN apk add --no-cache dos2unix                   3.2s
 => [pathforge_ai_backend  4/10] RUN npm install -g npm@latest                    10.1s
 => [pathforge_ai_backend  5/10] COPY package.json package-lock.json ./            0.0s
 => [pathforge_ai_backend  6/10] RUN npm ci                                       62.1s
 => [pathforge_ai_backend  7/10] COPY entrypoint.sh ./                             0.0s
 => [pathforge_ai_backend  8/10] RUN dos2unix ./entrypoint.sh && chmod +x ./entry  0.1s
 => [pathforge_ai_backend  9/10] COPY . .                                          2.0s
 => [pathforge_ai_backend 10/10] RUN npx prisma generate                           0.8s
 => [pathforge_ai_backend] exporting to image                                      0.7s
 => => exporting layers                                                            0.7s
 => => writing image sha256:eb23b8140f1ff6371391abcd97a08228f707d774ca9ec0934e54e  0.0s
 => => naming to docker.io/library/codepluse-platform-pathforge_ai_backend         0.0s
 => [pathforge_ai_backend] resolving provenance for metadata file                  0.0s
 => [pathforge_ai_frontend internal] load build definition from Dockerfile.fronte  0.0s
 => => transferring dockerfile: 1.41kB                                             0.0s
 => [pathforge_ai_frontend internal] load metadata for docker.io/library/nginx:al  4.9s
 => [pathforge_ai_frontend auth] library/nginx:pull token for registry-1.docker.i  0.0s
 => [pathforge_ai_frontend internal] load .dockerignore                            0.0s
 => => transferring context: 2B                                                    0.0s
 => [pathforge_ai_frontend production 1/6] FROM docker.io/library/nginx:alpine@s  64.8s
 => => resolve docker.io/library/nginx:alpine@sha256:65645c7bb6a0661892a8b03b89d0  0.0s
 => => sha256:6e771e15690e2fabf2332d3a3b744495411d6e0b00b2aea644 3.99MB / 3.99MB  23.7s
 => => sha256:e6557c42ebeaea010d8a8883fdacdc5a17dea1221416d0d980e206d 627B / 627B  1.2s
 => => sha256:65645c7bb6a0661892a8b03b89d0743208a18dd2f3f17a54e 10.33kB / 10.33kB  0.0s
 => => sha256:96868d9fa38f469a86d2f25787e43ee9ad330339d30be260a 10.80kB / 10.80kB  0.0s
 => => sha256:c60e446e49a0b607fd79968afdc54cedce67644990a3930925a 1.79MB / 1.79MB  2.9s
 => => sha256:63ffc0d1f14e4082b832c6a42e606e9a0384a526f16ddd720af 2.50kB / 2.50kB  0.0s
 => => sha256:d3282d7e6b7633cf153dce0ca1c72b6ea574edb0b34351f848fb16b 957B / 957B  2.7s
 => => sha256:a4ce1202d74643d4e4ce15787afc2402a18802e3d7bfd0f6bf60c91 405B / 405B  3.2s
 => => sha256:1ab010a063387e697fc32bb43022532c0e275d2e51fb65c2ac5 1.21kB / 1.21kB  3.5s
 => => sha256:37aca2470cdaf48d251d9308c09dc8735b45ebcbefada46537c 1.40kB / 1.40kB  3.7s
 => => sha256:9994ea1088e3f1d0eb3dea855f32e7e63742b2644c8611c1 16.03MB / 16.03MB  64.4s
 => => extracting sha256:6e771e15690e2fabf2332d3a3b744495411d6e0b00b2aea64419b58b  0.1s
 => => extracting sha256:c60e446e49a0b607fd79968afdc54cedce67644990a3930925add6ca  0.0s
 => => extracting sha256:e6557c42ebeaea010d8a8883fdacdc5a17dea1221416d0d980e206dd  0.0s
 => => extracting sha256:d3282d7e6b7633cf153dce0ca1c72b6ea574edb0b34351f848fb16b7  0.0s
 => => extracting sha256:a4ce1202d74643d4e4ce15787afc2402a18802e3d7bfd0f6bf60c912  0.0s
 => => extracting sha256:1ab010a063387e697fc32bb43022532c0e275d2e51fb65c2ac541e08  0.0s
 => => extracting sha256:37aca2470cdaf48d251d9308c09dc8735b45ebcbefada46537c99899  0.0s
 => => extracting sha256:9994ea1088e3f1d0eb3dea855f32e7e63742b2644c8611c124ba81bc  0.2s
 => [pathforge_ai_frontend internal] load build context                            0.0s
 => => transferring context: 658.15kB                                              0.0s
 => [pathforge_ai_frontend builder 3/7] COPY src/frontend/package*.json ./         0.1s
 => [pathforge_ai_frontend builder 4/7] COPY src/frontend/yarn.lock ./             0.0s
 => [pathforge_ai_frontend builder 5/7] RUN yarn install --frozen-lockfile       164.3s
 => => # yarn install v1.22.22                                                         
 => => # warning package-lock.json found. Your project contains lock files generated by
 => => #  Yarn. It is advised not to mix package managers in order to avoid resolution 
 => => # aused by unsynchronized lock files. To clear this warning, remove package-lock
 => => # [1/4] Resolving packages...                                                   
 => => # [2/4] Fetching packages...                                                    
 => [pathforge_ai_frontend production 2/6] COPY docker/nginx.conf /etc/nginx/ngin  0.4s
failed to receive status: rpc error: code = Unavailable desc = error reading from server: EOF
(venv) namnguyenhoai@vietprogrammer-2 codepluse-platform % docker-compose up --build
Compose can now delegate builds to bake for better performance.
 To do so, set COMPOSE_BAKE=true.
[+] Building 405.5s (34/68)                                        docker:desktop-linux
 => [pathforge_ai_agent_service builder  2/14] RUN apt-get update && apt-get in  316.2s
 => => # Get:75 http://deb.debian.org/debian bookworm/main arm64 curl arm64 7.88.1-10+d
 => => # eb12u12 [309 kB]                                                              
 => => # Get:76 http://deb.debian.org/debian bookworm/main arm64 libassuan0 arm64 2.5.5
 => => # -5 [45.9 kB]                                                                  
 => => # Get:77 http://deb.debian.org/debian bookworm/main arm64 gpgconf arm64 2.2.40-1
 => => # .1 [557 kB]                                                                   
 => => extracting sha256:c60e446e49a0b607fd79968afdc54cedce67644990a3930925add6ca  0.0s
 => => extracting sha256:e6557c42ebeaea010d8a8883fdacdc5a17dea1221416d0d980e206dd  0.0s
 => => extracting sha256:d3282d7e6b7633cf153dce0ca1c72b6ea574edb0b34351f848[+] Building 405.6s (34/68)                                        docker:desktop-linuxcting sha256:a4ce1202d74643d4e4ce15787afc2402a18802e3d7bfd0f6bf
 => [pathforge_ai_agent_service builder  2/14] RUN apt-get update && apt-get in  316.3scting sha256:1ab010a063387e697fc32bb43022532c0e275d2e51fb65c2ac
 => => # Get:75 http://deb.debian.org/debian bookworm/main arm64 curl arm64 7.88.1-10+dcting sha256:37aca2470cdaf48d251d9308c09dc8735b45ebcbefada46537
 => => # eb12u12 [309 kB]                                                              cting sha256:9994ea1088e3f1d0eb3dea855f32e7e63742b2644c8611c124
 => => # Get:76 http://deb.debian.org/debian bookworm/main arm64 libassuan0 arm64 2.5.5ge_ai_frontend internal] load build context                    
 => => # -5 [45.9 kB]                                                                  ferring context: 658.15kB                                      
 => => # Get:77 http://deb.debian.org/debian bookworm/main arm64 gpgconf arm64 2.2.40-1ge_ai_frontend builder 3/7] COPY src/frontend/package*.json ./ 
 => => # .1 [557 kB]                                                                   ge_ai_frontend builder 4/7] COPY src/frontend/yarn.lock ./     
 => => extracting sha256:c60e446e49a0b607fd79968afdc54cedce67644990a3930925add6ca  0.0sge_ai_frontend builder 5/7] RUN yarn install --frozen-lockfile 
 => => extracting sha256:e6557c42ebeaea010d8a8883fdacdc5a17dea1221416d0d980e206dd  0.0sge_ai_frontend production 2/6] COPY docker/nginx.conf /etc/ngin
 => => extracting sha256:d3282d7e6b7633cf153dce0ca1c72b6ea574edb0b34351f848[+] Building 405.8s (34/68)                                        docker:desktop-linuxcting sha256:a4ce1202d74643d4e4ce15787afc2402a18802e3d7bfd0f6bf
 => [pathforge_ai_agent_service builder  2/14] RUN apt-get update && apt-get in  316.5scting sha256:1ab010a063387e697fc32bb43022532c0e275d2e51fb65c2ac
 => => # Get:75 http://deb.debian.org/debian bookworm/main arm64 curl arm64 7.88.1-10+dcting sha256:37aca2470cdaf48d251d9308c09dc8735b45ebcbefada46537
 => => # eb12u12 [309 kB]                                                              cting sha256:9994ea1088e3f1d0eb3dea855f32e7e63742b2644c8611c124
 => => # Get:76 http://deb.debian.org/debian bookworm/main arm64 libassuan0 arm64 2.5.5ge_ai_frontend internal] load build context                    
 => => # -5 [45.9 kB]                                                                  ferring context: 658.15kB                                      
 => => # Get:77 http://deb.debian.org/debian bookworm/main arm64 gpgconf arm64 2.2.40-1ge_ai_frontend builder 3/7] COPY src/frontend/package*.json ./ 
 => => # .1 [557 kB]                                                                   ge_ai_frontend builder 4/7] COPY src/frontend/yarn.lock ./     
 => => extracting sha256:c60e446e49a0b607fd79968afdc54cedce67644990a3930925add6ca  0.0sge_ai_frontend builder 5/7] RUN yarn install --frozen-lockfile 
 => => extracting sha256:e6557c42ebeaea010d8a8883fdacdc5a17dea1221416d0d980e206dd  0.0sge_ai_frontend production 2/6] COPY docker/nginx.conf /etc/ngin
 => => extracting sha256:d3282d7e6b7633cf153dce0ca1c72b6ea574edb0b34351f848[+] Building 405.9s (34/68)                                        docker:desktop-linuxcting sha256:a4ce1202d74643d4e4ce15787afc2402a18802e3d7bfd0f6bf
 => [pathforge_ai_agent_service builder  2/14] RUN apt-get update && apt-get in  316.6scting sha256:1ab010a063387e697fc32bb43022532c0e275d2e51fb65c2ac
 => => # Get:75 http://deb.debian.org/debian bookworm/main arm64 curl arm64 7.88.1-10+dcting sha256:37aca2470cdaf48d251d9308c09dc8735b45ebcbefada46537
 => => # eb12u12 [309 kB]                                                              cting sha256:9994ea1088e3f1d0eb3dea855f32e7e63742b2644c8611c124
 => => # Get:76 http://deb.debian.org/debian bookworm/main arm64 libassuan0 arm64 2.5.5ge_ai_frontend internal] load build context                    
 => => # -5 [45.9 kB]                                                                  ferring context: 658.15kB                                      
 => => # Get:77 http://deb.debian.org/debian bookworm/main arm64 gpgconf arm64 2.2.40-1ge_ai_frontend builder 3/7] COPY src/frontend/package*.json ./ 
 => => # .1 [557 kB]                                                                   ge_ai_frontend builder 4/7] COPY src/frontend/yarn.lock ./     
 => => extracting sha256:c60e446e49a0b607fd79968afdc54cedce67644990a3930925add6ca  0.0sge_ai_frontend builder 5/7] RUN yarn install --frozen-lockfile 
 => => extracting sha256:e6557c42ebeaea010d8a8883fdacdc5a17dea1221416d0d980e206dd  0.0sge_ai_frontend production 2/6] COPY docker/nginx.conf /etc/ngin
 => => extracting sha256:d3282d7e6b7633cf153dce0ca1c72b6ea574edb0b34351f848[+] Building 406.1s (34/68)                                        docker:desktop-linuxcting sha256:a4ce1202d74643d4e4ce15787afc2402a18802e3d7bfd0f6bf
 => [pathforge_ai_agent_service builder  2/14] RUN apt-get update && apt-get in  316.8scting sha256:1ab010a063387e697fc32bb43022532c0e275d2e51fb65c2ac
 => => # Get:75 http://deb.debian.org/debian bookworm/main arm64 curl arm64 7.88.1-10+dcting sha256:37aca2470cdaf48d251d9308c09dc8735b45ebcbefada46537
 => => # eb12u12 [309 kB]                                                              cting sha256:9994ea1088e3f1d0eb3dea855f32e7e63742b2644c8611c124
 => => # Get:76 http://deb.debian.org/debian bookworm/main arm64 libassuan0 arm64 2.5.5ge_ai_frontend internal] load build context                    
 => => # -5 [45.9 kB]                                                                  ferring context: 658.15kB                                      
 => => # Get:77 http://deb.debian.org/debian bookworm/main arm64 gpgconf arm64 2.2.40-1ge_ai_frontend builder 3/7] COPY src/frontend/package*.json ./ 
 => => # .1 [557 kB]                                                                   ge_ai_frontend builder 4/7] COPY src/frontend/yarn.lock ./     
 => => extracting sha256:c60e446e49a0b607fd79968afdc54cedce67644990a3930925add6ca  0.0sge_ai_frontend builder 5/7] RUN yarn install --frozen-lockfile 
 => => extracting sha256:e6557c42ebeaea010d8a8883fdacdc5a17dea1221416d0d980e206dd  0.0sge_ai_frontend production 2/6] COPY docker/nginx.conf /etc/ngin
 => => extracting sha256:d3282d7e6b7633cf153dce0ca1c72b6ea574edb0b34351f848[+] Building 406.2s (34/68)                                        docker:desktop-linuxcting sha256:a4ce1202d74643d4e4ce15787afc2402a18802e3d7bfd0f6bf
 => [pathforge_ai_agent_service builder  2/14] RUN apt-get update && apt-get in  316.9scting sha256:1ab010a063387e697fc32bb43022532c0e275d2e51fb65c2ac
 => => # Get:75 http://deb.debian.org/debian bookworm/main arm64 curl arm64 7.88.1-10+dcting sha256:37aca2470cdaf48d251d9308c09dc8735b45ebcbefada46537
 => => # eb12u12 [309 kB]                                                              cting sha256:9994ea1088e3f1d0eb3dea855f32e7e63742b2644c8611c124
 => => # Get:76 http://deb.debian.org/debian bookworm/main arm64 libassuan0 arm64 2.5.5ge_ai_frontend internal] load build context                    
 => => # -5 [45.9 kB]                                                                  ferring context: 658.15kB                                      
 => => # Get:77 http://deb.debian.org/debian bookworm/main arm64 gpgconf arm64 2.2.40-1ge_ai_frontend builder 3/7] COPY src/frontend/package*.json ./ 
 => => # .1 [557 kB]                                                                   ge_ai_frontend builder 4/7] COPY src/frontend/yarn.lock ./     
 => => extracting sha256:c60e446e49a0b607fd79968afdc54cedce67644990a3930925add6ca  0.0sge_ai_frontend builder 5/7] RUN yarn install --frozen-lockfile 
 => => extracting sha256:e6557c42ebeaea010d8a8883fdacdc5a17dea1221416d0d980e206dd  0.0sge_ai_frontend production 2/6] COPY docker/nginx.conf /etc/ngin
 => => extracting sha256:d3282d7e6b7633cf153dce0ca1c72b6ea574edb0b34351f848[+] Building 406.4s (34/68)                                        docker:desktop-linuxcting sha256:a4ce1202d74643d4e4ce15787afc2402a18802e3d7bfd0f6bf
 => [pathforge_ai_agent_service builder  2/14] RUN apt-get update && apt-get in  317.1scting sha256:1ab010a063387e697fc32bb43022532c0e275d2e51fb65c2ac
 => => # Get:75 http://deb.debian.org/debian bookworm/main arm64 curl arm64 7.88.1-10+dcting sha256:37aca2470cdaf48d251d9308c09dc8735b45ebcbefada46537
 => => # eb12u12 [309 kB]                                                              cting sha256:9994ea1088e3f1d0eb3dea855f32e7e63742b2644c8611c124
 => => # Get:76 http://deb.debian.org/debian bookworm/main arm64 libassuan0 arm64 2.5.5ge_ai_frontend internal] load build context                    
 => => # -5 [45.9 kB]                                                                  ferring context: 658.15kB                                      
 => => # Get:77 http://deb.debian.org/debian bookworm/main arm64 gpgconf arm64 2.2.40-1ge_ai_frontend builder 3/7] COPY src/frontend/package*.json ./ 
 => => # .1 [557 kB]                                                                   ge_ai_frontend builder 4/7] COPY src/frontend/yarn.lock ./     
 => => extracting sha256:c60e446e49a0b607fd79968afdc54cedce67644990a3930925add6ca  0.0sge_ai_frontend builder 5/7] RUN yarn install --frozen-lockfile 
 => => extracting sha256:e6557c42ebeaea010d8a8883fdacdc5a17dea1221416d0d980e206dd  0.0sge_ai_frontend production 2/6] COPY docker/nginx.conf /etc/ngin
 => => extracting sha256:d3282d7e6b7633cf153dce0ca1c72b6ea574edb0b34351f848[+] Building 406.5s (34/68)                                        docker:desktop-linuxcting sha256:a4ce1202d74643d4e4ce15787afc2402a18802e3d7bfd0f6bf
 => [pathforge_ai_agent_service builder  2/14] RUN apt-get update && apt-get in  317.2scting sha256:1ab010a063387e697fc32bb43022532c0e275d2e51fb65c2ac
 => => # Get:75 http://deb.debian.org/debian bookworm/main arm64 curl arm64 7.88.1-10+dcting sha256:37aca2470cdaf48d251d9308c09dc8735b45ebcbefada46537
 => => # eb12u12 [309 kB]                                                              cting sha256:9994ea1088e3f1d0eb3dea855f32e7e63742b2644c8611c124
 => => # Get:76 http://deb.debian.org/debian bookworm/main arm64 libassuan0 arm64 2.5.5ge_ai_frontend internal] load build context                    
 => => # -5 [45.9 kB]                                                                  ferring context: 658.15kB                                      
 => => # Get:77 http://deb.debian.org/debian bookworm/main arm64 gpgconf arm64 2.2.40-1ge_ai_frontend builder 3/7] COPY src/frontend/package*.json ./ 
 => => # .1 [557 kB]                                                                   ge_ai_frontend builder 4/7] COPY src/frontend/yarn.lock ./     
 => => extracting sha256:c60e446e49a0b607fd79968afdc54cedce67644990a3930925add6ca  0.0sge_ai_frontend builder 5/7] RUN yarn install --frozen-lockfile 
 => => extracting sha256:e6557c42ebeaea010d8a8883fdacdc5a17dea1221416d0d980e206dd  0.0sge_ai_frontend production 2/6] COPY docker/nginx.conf /etc/ngin
 => => extracting sha256:d3282d7e6b7633cf153dce0ca1c72b6ea574edb0b34351f848[+] Building 406.7s (34/68)                                        docker:desktop-linuxcting sha256:a4ce1202d74643d4e4ce15787afc2402a18802e3d7bfd0f6bf
 => [pathforge_ai_agent_service builder  2/14] RUN apt-get update && apt-get in  317.4scting sha256:1ab010a063387e697fc32bb43022532c0e275d2e51fb65c2ac
 => => # Get:75 http://deb.debian.org/debian bookworm/main arm64 curl arm64 7.88.1-10+dcting sha256:37aca2470cdaf48d251d9308c09dc8735b45ebcbefada46537
 => => # eb12u12 [309 kB]                                                              cting sha256:9994ea1088e3f1d0eb3dea855f32e7e63742b2644c8611c124
 => => # Get:76 http://deb.debian.org/debian bookworm/main arm64 libassuan0 arm64 2.5.5ge_ai_frontend internal] load build context                    
 => => # -5 [45.9 kB]                                                                  ferring context: 658.15kB                                      
 => => # Get:77 http://deb.debian.org/debian bookworm/main arm64 gpgconf arm64 2.2.40-1ge_ai_frontend builder 3/7] COPY src/frontend/package*.json ./ 
 => => # .1 [557 kB]                                                                   ge_ai_frontend builder 4/7] COPY src/frontend/yarn.lock ./     
 => => extracting sha256:c60e446e49a0b607fd79968afdc54cedce67644990a3930925add6ca  0.0sge_ai_frontend builder 5/7] RUN yarn install --frozen-lockfile 
 => => extracting sha256:e6557c42ebeaea010d8a8883fdacdc5a17dea1221416d0d980e206dd  0.0sge_ai_frontend production 2/6] COPY docker/nginx.conf /etc/ngin
 => => extracting sha256:d3282d7e6b7633cf153dce0ca1c72b6ea574edb0b34351f848[+] Building 406.8s (34/68)                                        docker:desktop-linuxcting sha256:a4ce1202d74643d4e4ce15787afc2402a18802e3d7bfd0f6bf
 => [pathforge_ai_agent_service builder  2/14] RUN apt-get update && apt-get in  317.5scting sha256:1ab010a063387e697fc32bb43022532c0e275d2e51fb65c2ac
 => => # Get:75 http://deb.debian.org/debian bookworm/main arm64 curl arm64 7.88.1-10+dcting sha256:37aca2470cdaf48d251d9308c09dc8735b45ebcbefada46537
 => => # eb12u12 [309 kB]                                                              cting sha256:9994ea1088e3f1d0eb3dea855f32e7e63742b2644c8611c124
 => => # Get:76 http://deb.debian.org/debian bookworm/main arm64 libassuan0 arm64 2.5.5ge_ai_frontend internal] load build context                    
 => => # -5 [45.9 kB]                                                                  ferring context: 658.15kB                                      
 => => # Get:77 http://deb.debian.org/debian bookworm/main arm64 gpgconf arm64 2.2.40-1ge_ai_frontend builder 3/7] COPY src/frontend/package*.json ./ 
 => => # .1 [557 kB]                                                                   ge_ai_frontend builder 4/7] COPY src/frontend/yarn.lock ./     
 => => extracting sha256:c60e446e49a0b607fd79968afdc54cedce67644990a3930925add6ca  0.0sge_ai_frontend builder 5/7] RUN yarn install --frozen-lockfile 
 => => extracting sha256:e6557c42ebeaea010d8a8883fdacdc5a17dea1221416d0d980e206dd  0.0sge_ai_frontend production 2/6] COPY docker/nginx.conf /etc/ngin
 => => extracting sha256:d3282d7e6b7633cf153dce0ca1c72b6ea574edb0b34351f848[+] Building 407.0s (34/68)                                        docker:desktop-linuxcting sha256:a4ce1202d74643d4e4ce15787afc2402a18802e3d7bfd0f6bf
 => [pathforge_ai_agent_service builder  2/14] RUN apt-get update && apt-get in  317.7scting sha256:1ab010a063387e697fc32bb43022532c0e275d2e51fb65c2ac
 => => # Get:75 http://deb.debian.org/debian bookworm/main arm64 curl arm64 7.88.1-10+dcting sha256:37aca2470cdaf48d251d9308c09dc8735b45ebcbefada46537
 => => # eb12u12 [309 kB]                                                              cting sha256:9994ea1088e3f1d0eb3dea855f32e7e63742b2644c8611c124
 => => # Get:76 http://deb.debian.org/debian bookworm/main arm64 libassuan0 arm64 2.5.5ge_ai_frontend internal] load build context                    
 => => # -5 [45.9 kB]                                                                  ferring context: 658.15kB                                      
 => => # Get:77 http://deb.debian.org/debian bookworm/main arm64 gpgconf arm64 2.2.40-1ge_ai_frontend builder 3/7] COPY src/frontend/package*.json ./ 
 => => # .1 [557 kB]                                                                   ge_ai_frontend builder 4/7] COPY src/frontend/yarn.lock ./     
 => => extracting sha256:c60e446e49a0b607fd79968afdc54cedce67644990a3930925add6ca  0.0sge_ai_frontend builder 5/7] RUN yarn install --frozen-lockfile 
 => => extracting sha256:e6557c42ebeaea010d8a8883fdacdc5a17dea1221416d0d980e206dd  0.0sge_ai_frontend production 2/6] COPY docker/nginx.conf /etc/ngin
 => => extracting sha256:d3282d7e6b7633cf153dce0ca1c72b6ea574edb0b34351f848[+] Building 407.1s (34/68)                                        docker:desktop-linuxcting sha256:a4ce1202d74643d4e4ce15787afc2402a18802e3d7bfd0f6bf
 => [pathforge_ai_agent_service builder  2/14] RUN apt-get update && apt-get in  317.8scting sha256:1ab010a063387e697fc32bb43022532c0e275d2e51fb65c2ac
 => => # Get:75 http://deb.debian.org/debian bookworm/main arm64 curl arm64 7.88.1-10+dcting sha256:37aca2470cdaf48d251d9308c09dc8735b45ebcbefada46537
 => => # eb12u12 [309 kB]                                                              cting sha256:9994ea1088e3f1d0eb3dea855f32e7e63742b2644c8611c124
 => => # Get:76 http://deb.debian.org/debian bookworm/main arm64 libassuan0 arm64 2.5.5ge_ai_frontend internal] load build context                    
 => => # -5 [45.9 kB]                                                                  ferring context: 658.15kB                                      
 => => # Get:77 http://deb.debian.org/debian bookworm/main arm64 gpgconf arm64 2.2.40-1ge_ai_frontend builder 3/7] COPY src/frontend/package*.json ./ 
 => => # .1 [557 kB]                                                                   ge_ai_frontend builder 4/7] COPY src/frontend/yarn.lock ./     
 => => extracting sha256:c60e446e49a0b607fd79968afdc54cedce67644990a3930925add6ca  0.0sge_ai_frontend builder 5/7] RUN yarn install --frozen-lockfile 
 => => extracting sha256:e6557c42ebeaea010d8a8883fdacdc5a17dea1221416d0d980e206dd  0.0sge_ai_frontend production 2/6] COPY docker/nginx.conf /etc/ngin
 => => extracting sha256:d3282d7e6b7633cf153dce0ca1c72b6ea574edb0b34351f848[+] Building 407.3s (34/68)                                        docker:desktop-linuxcting sha256:a4ce1202d74643d4e4ce15787afc2402a18802e3d7bfd0f6bf
 => [pathforge_ai_agent_service builder  2/14] RUN apt-get update && apt-get in  318.0scting sha256:1ab010a063387e697fc32bb43022532c0e275d2e51fb65c2ac
 => => # Get:75 http://deb.debian.org/debian bookworm/main arm64 curl arm64 7.88.1-10+dcting sha256:37aca2470cdaf48d251d9308c09dc8735b45ebcbefada46537
 => => # eb12u12 [309 kB]                                                              cting sha256:9994ea1088e3f1d0eb3dea855f32e7e63742b2644c8611c124
 => => # Get:76 http://deb.debian.org/debian bookworm/main arm64 libassuan0 arm64 2.5.5ge_ai_frontend internal] load build context                    
 => => # -5 [45.9 kB]                                                                  ferring context: 658.15kB                                      
 => => # Get:77 http://deb.debian.org/debian bookworm/main arm64 gpgconf arm64 2.2.40-1ge_ai_frontend builder 3/7] COPY src/frontend/package*.json ./ 
 => => # .1 [557 kB]                                                                   ge_ai_frontend builder 4/7] COPY src/frontend/yarn.lock ./     
 => => extracting sha256:c60e446e49a0b607fd79968afdc54cedce67644990a3930925add6ca  0.0sge_ai_frontend builder 5/7] RUN yarn install --frozen-lockfile 
 => => extracting sha256:e6557c42ebeaea010d8a8883fdacdc5a17dea1221416d0d980e206dd  0.0sge_ai_frontend production 2/6] COPY docker/nginx.conf /etc/ngin
 => => extracting sha256:d3282d7e6b7633cf153dce0ca1c72b6ea574edb0b34351f848[+] Building 407.4s (34/68)                                        docker:desktop-linuxcting sha256:a4ce1202d74643d4e4ce15787afc2402a18802e3d7bfd0f6bf
 => [pathforge_ai_agent_service builder  2/14] RUN apt-get update && apt-get in  318.1scting sha256:1ab010a063387e697fc32bb43022532c0e275d2e51fb65c2ac
 => => # Get:75 http://deb.debian.org/debian bookworm/main arm64 curl arm64 7.88.1-10+dcting sha256:37aca2470cdaf48d251d9308c09dc8735b45ebcbefada46537
 => => # eb12u12 [309 kB]                                                              cting sha256:9994ea1088e3f1d0eb3dea855f32e7e63742b2644c8611c124
 => => # Get:76 http://deb.debian.org/debian bookworm/main arm64 libassuan0 arm64 2.5.5ge_ai_frontend internal] load build context                    
 => => # -5 [45.9 kB]                                                                  ferring context: 658.15kB                                      
 => => # Get:77 http://deb.debian.org/debian bookworm/main arm64 gpgconf arm64 2.2.40-1ge_ai_frontend builder 3/7] COPY src/frontend/package*.json ./ 
 => => # .1 [557 kB]                                                                   ge_ai_frontend builder 4/7] COPY src/frontend/yarn.lock ./     
 => => extracting sha256:c60e446e49a0b607fd79968afdc54cedce67644990a3930925add6ca  0.0sge_ai_frontend builder 5/7] RUN yarn install --frozen-lockfile 
 => => extracting sha256:e6557c42ebeaea010d8a8883fdacdc5a17dea1221416d0d980e206dd  0.0sge_ai_frontend production 2/6] COPY docker/nginx.conf /etc/ngin
 => => extracting sha256:d3282d7e6b7633cf153dce0ca1c72b6ea574edb0b34351f848[+] Building 407.6s (34/68)                                        docker:desktop-linuxcting sha256:a4ce1202d74643d4e4ce15787afc2402a18802e3d7bfd0f6bf
 => [pathforge_ai_agent_service builder  2/14] RUN apt-get update && apt-get in  318.3scting sha256:1ab010a063387e697fc32bb43022532c0e275d2e51fb65c2ac
 => => # Get:75 http://deb.debian.org/debian bookworm/main arm64 curl arm64 7.88.1-10+dcting sha256:37aca2470cdaf48d251d9308c09dc8735b45ebcbefada46537
 => => # eb12u12 [309 kB]                                                              cting sha256:9994ea1088e3f1d0eb3dea855f32e7e63742b2644c8611c124
 => => # Get:76 http://deb.debian.org/debian bookworm/main arm64 libassuan0 arm64 2.5.5ge_ai_frontend internal] load build context                    
 => => # -5 [45.9 kB]                                                                  ferring context: 658.15kB                                      
 => => # Get:77 http://deb.debian.org/debian bookworm/main arm64 gpgconf arm64 2.2.40-1ge_ai_frontend builder 3/7] COPY src/frontend/package*.json ./ 
 => => # .1 [557 kB]                                                                   ge_ai_frontend builder 4/7] COPY src/frontend/yarn.lock ./     
 => => extracting sha256:c60e446e49a0b607fd79968afdc54cedce67644990a3930925add6ca  0.0sge_ai_frontend builder 5/7] RUN yarn install --frozen-lockfile 
 => => extracting sha256:e6557c42ebeaea010d8a8883fdacdc5a17dea1221416d0d980e206dd  0.0sge_ai_frontend production 2/6] COPY docker/nginx.conf /etc/ngin
 => => extracting sha256:d3282d7e6b7633cf153dce0ca1c72b6ea574edb0b34351f848[+] Building 407.7s (34/68)                                        docker:desktop-linuxcting sha256:a4ce1202d74643d4e4ce15787afc2402a18802e3d7bfd0f6bf
 => [pathforge_ai_agent_service builder  2/14] RUN apt-get update && apt-get in  318.4scting sha256:1ab010a063387e697fc32bb43022532c0e275d2e51fb65c2ac
 => => # Get:75 http://deb.debian.org/debian bookworm/main arm64 curl arm64 7.88.1-10+dcting sha256:37aca2470cdaf48d251d9308c09dc8735b45ebcbefada46537
 => => # eb12u12 [309 kB]                                                              cting sha256:9994ea1088e3f1d0eb3dea855f32e7e63742b2644c8611c124
 => => # Get:76 http://deb.debian.org/debian bookworm/main arm64 libassuan0 arm64 2.5.5ge_ai_frontend internal] load build context                    
 => => # -5 [45.9 kB]                                                                  ferring context: 658.15kB                                      
 => => # Get:77 http://deb.debian.org/debian bookworm/main arm64 gpgconf arm64 2.2.40-1ge_ai_frontend builder 3/7] COPY src/frontend/package*.json ./ 
 => => # .1 [557 kB]                                                                   ge_ai_frontend builder 4/7] COPY src/frontend/yarn.lock ./     
 => => extracting sha256:c60e446e49a0b607fd79968afdc54cedce67644990a3930925add6ca  0.0sge_ai_frontend builder 5/7] RUN yarn install --frozen-lockfile 
 => => extracting sha256:e6557c42ebeaea010d8a8883fdacdc5a17dea1221416d0d980e206dd  0.0sge_ai_frontend production 2/6] COPY docker/nginx.conf /etc/ngin
 => => extracting sha256:d3282d7e6b7633cf153dce0ca1c72b6ea574edb0b34351f848[+] Building 407.9s (34/68)                                        docker:desktop-linuxcting sha256:a4ce1202d74643d4e4ce15787afc2402a18802e3d7bfd0f6bf
 => [pathforge_ai_agent_service builder  2/14] RUN apt-get update && apt-get in  318.6scting sha256:1ab010a063387e697fc32bb43022532c0e275d2e51fb65c2ac
 => => # Get:75 http://deb.debian.org/debian bookworm/main arm64 curl arm64 7.88.1-10+dcting sha256:37aca2470cdaf48d251d9308c09dc8735b45ebcbefada46537
 => => # eb12u12 [309 kB]                                                              cting sha256:9994ea1088e3f1d0eb3dea855f32e7e63742b2644c8611c124
 => => # Get:76 http://deb.debian.org/debian bookworm/main arm64 libassuan0 arm64 2.5.5ge_ai_frontend internal] load build context                    
 => => # -5 [45.9 kB]                                                                  ferring context: 658.15kB                                      
 => => # Get:77 http://deb.debian.org/debian bookworm/main arm64 gpgconf arm64 2.2.40-1ge_ai_frontend builder 3/7] COPY src/frontend/package*.json ./ 
 => => # .1 [557 kB]                                                                   ge_ai_frontend builder 4/7] COPY src/frontend/yarn.lock ./     
 => => extracting sha256:c60e446e49a0b607fd79968afdc54cedce67644990a3930925add6ca  0.0sge_ai_frontend builder 5/7] RUN yarn install --frozen-lockfile 
 => => extracting sha256:e6557c42ebeaea010d8a8883fdacdc5a17dea1221416d0d980e206dd  0.0sge_ai_frontend production 2/6] COPY docker/nginx.conf /etc/ngin
 => => extracting sha256:d3282d7e6b7633cf153dce0ca1c72b6ea574edb0b34351f848[+] Building 408.0s (34/68)                                        docker:desktop-linuxcting sha256:a4ce1202d74643d4e4ce15787afc2402a18802e3d7bfd0f6bf
 => [pathforge_ai_agent_service builder  2/14] RUN apt-get update && apt-get in  318.7scting sha256:1ab010a063387e697fc32bb43022532c0e275d2e51fb65c2ac
 => => # Get:75 http://deb.debian.org/debian bookworm/main arm64 curl arm64 7.88.1-10+dcting sha256:37aca2470cdaf48d251d9308c09dc8735b45ebcbefada46537
 => => # eb12u12 [309 kB]                                                              cting sha256:9994ea1088e3f1d0eb3dea855f32e7e63742b2644c8611c124
 => => # Get:76 http://deb.debian.org/debian bookworm/main arm64 libassuan0 arm64 2.5.5ge_ai_frontend internal] load build context                    
 => => # -5 [45.9 kB]                                                                  ferring context: 658.15kB                                      
 => => # Get:77 http://deb.debian.org/debian bookworm/main arm64 gpgconf arm64 2.2.40-1ge_ai_frontend builder 3/7] COPY src/frontend/package*.json ./ 
 => => # .1 [557 kB]                                                                   ge_ai_frontend builder 4/7] COPY src/frontend/yarn.lock ./     
 => => extracting sha256:c60e446e49a0b607fd79968afdc54cedce67644990a3930925add6ca  0.0sge_ai_frontend builder 5/7] RUN yarn install --frozen-lockfile 
 => => extracting sha256:e6557c42ebeaea010d8a8883fdacdc5a17dea1221416d0d980e206dd  0.0sge_ai_frontend production 2/6] COPY docker/nginx.conf /etc/ngin
 => => extracting sha256:d3282d7e6b7633cf153dce0ca1c72b6ea574edb0b34351f848[+] Building 835.3s (48/70)                           docker:desktop-linux
 => => extracting sha256:d3282d7e6b7633cf153dce0ca1c72b6ea574edb0b34  0.0sf
 => => extracting sha256:a4ce1202d74643d4e4ce15787afc2402a18802e3d7b  0.0s
 => => extracting sha256:1ab010a063387e697fc32bb43022532c0e275d2e51f  0.0sc
 => => extracting sha256:37aca2470cdaf48d251d9308c09dc8735b45ebcbefa  0.0s
 => => extracting sha256:9994ea1088e3f1d0eb3dea855f32e7e63742b2644c8  0.2s7
 => [pathforge_ai_frontend internal] load build context               0.0s
 => => transferring context: 658.15kB                                 0.0s4
 => [pathforge_ai_frontend builder 3/7] COPY src/frontend/package*.j  1.1s
 => [pathforge_ai_frontend builder 4/7] COPY src/frontend/yarn.lock   0.0s 
 => [pathforge_ai_frontend builder 5/7] RUN yarn install --frozen-  219.9s
 => [pathforge_ai_frontend production 2/6] COPY docker/nginx.conf /e  0.2s 
 => [pathforge_ai_frontend builder 6/7] COPY src/frontend/ ./         0.3s
 => [pathforge_ai_frontend builder 7/7] RUN yarn build                6.3s 
 => [pathforge_ai_frontend production 3/6] COPY --from=builder /app/  0.2s
 => [pathforge_ai_frontend production 4/6] RUN addgroup -g 1001 -S n  0.1s 
 => [pathforge_ai_frontend production 5/6] RUN chown -R nextjs:nodej  0.1s
 => [pathforge_ai_frontend production 6/6] RUN mkdir -p /tmp/nginx/c  0.2s 
 => [pathforge_ai_frontend] exporting to image                        0.2s
 => => exporting layers                                               0.1sn
 => => exporting manifest sha256:2a4fce5f5cc6475825f66367911b39e4d1b  0.0s
 => => exporting config sha256:4e3704c12cde6757d3e39806e48e55f166d44  0.0s
 => => exporting attestation manifest sha256:a58ff92a0a231a959978bb2  0.0s
 => => exporting manifest list sha256:f8c3133c6c01e677cdf96de2451a18  0.0s
 => => naming to docker.io/library/codepluse-platform-pathforge_ai_f  0.0s
 => => unpacking to docker.io/library/codepluse-platform-pathforge_a  0.0s
 => [pathforge_ai_frontend] resolving provenance for metadata file    0.0s
 => [pathforge_ai_agent_service builder  3/14] WORKDIR /app           0.1s
 => [pathforge_ai_agent_service builder  4/14] RUN pip install --no  20.7s
 => [pathforge_ai_agent_service builder  5/14] COPY pyproject.toml .  0.0s
 => [pathforge_ai_agent_service builder  6/14] COPY uv.lock .         0.0s
 => [pathforge_ai_agent_service builder  7/14] RUN uv sync --froze  304.6s
 => => #  Downloading ruff                                                
 => => #  Downloading mypy                                                
 => => #  Downloading botocore                                            
 => => #  Downloading numpy                                               
 => => #  Downloading onnxruntime                                         
 => => #  Downloading pandas                                  